"""
MAPPO训练脚本 - 基于单层AGV-Task-AGV注意力机制的MAPPO多AGV调度优化系统
实现完整的训练流程，包括课程学习和注意力机制集成
"""

import os
import torch
import numpy as np
import ray
from ray import tune
from ray.rllib.algorithms.ppo import PPOConfig
from ray.rllib.env.multi_agent_env import MultiAgentEnv
from ray.rllib.models import ModelCatalog
from ray.rllib.models.torch.torch_modelv2 import TorchModelV2
from ray.rllib.utils.framework import try_import_torch
from typing import Dict, Any, Optional, Tuple

from envs.multi_agv_env import MultiAGVEnv
from models.mappo_with_attention import PolicyNetwork, ValueNetwork
from models.curriculum_learning import CurriculumLearningManager
from utils.config import MAPPO_CONFIG, ENV_CONFIG, CURRICULUM_CONFIG

torch, nn = try_import_torch()


class AttentionMAPPOModel(TorchModelV2, nn.Module):
    """集成注意力机制的MAPPO模型"""
    
    def __init__(self, obs_space, action_space, num_outputs, model_config, name):
        TorchModelV2.__init__(self, obs_space, action_space, num_outputs, model_config, name)
        nn.Module.__init__(self)
        
        # 策略网络
        self.policy_net = PolicyNetwork()
        
        # 价值网络
        self.value_net = ValueNetwork()
        
        # 存储注意力权重
        self._attention_weights = None
        
    def forward(self, input_dict, state, seq_lens):
        """前向传播"""
        obs = input_dict["obs"]
        batch_size = obs.shape[0]
        
        # 解析观测
        agv_positions, task_positions, agv_loads, task_weights, task_status = self._parse_observations(obs)
        
        # 策略网络前向传播
        task_logits, motion_logits, attention_info = self.policy_net(
            obs.unsqueeze(1),  # 添加AGV维度
            agv_positions.unsqueeze(1),
            task_positions,
            agv_loads.unsqueeze(1),
            task_weights,
            task_status
        )
        
        # 存储注意力权重
        self._attention_weights = attention_info
        
        # 合并动作logits
        combined_logits = torch.cat([
            task_logits.squeeze(1),  # 移除AGV维度
            motion_logits.squeeze(1)
        ], dim=-1)
        
        return combined_logits, state
    
    def value_function(self):
        """价值函数"""
        # 这里需要全局状态，简化实现
        # 在实际使用中，需要从环境获取全局状态
        dummy_global_state = torch.zeros(1, 92)  # 全局状态维度
        dummy_attention_features = torch.zeros(1, 256)  # 注意力特征维度
        
        value = self.value_net(dummy_global_state, dummy_attention_features)
        return value.squeeze(-1)
    
    def _parse_observations(self, obs):
        """解析观测数据"""
        batch_size = obs.shape[0]
        
        # 简化实现：从观测中提取必要信息
        # 实际实现中需要根据观测结构进行解析
        
        # AGV位置（假设在观测的前2维）
        agv_positions = obs[:, :2]
        
        # 任务位置（假设固定）
        task_positions = torch.tensor([[3, 2], [6, 2], [9, 2], [12, 2]], dtype=torch.float32)
        task_positions = task_positions.unsqueeze(0).expand(batch_size, -1, -1)
        
        # AGV载重（假设在观测的第3维）
        agv_loads = obs[:, 2]
        
        # 任务重量（假设固定）
        task_weights = torch.tensor([5, 10, 5, 10], dtype=torch.float32)
        task_weights = task_weights.unsqueeze(0).expand(batch_size, -1)
        
        # 任务状态（假设固定为可用）
        task_status = torch.zeros(batch_size, 4, dtype=torch.long)
        
        return agv_positions, task_positions, agv_loads, task_weights, task_status


def create_training_config():
    """创建训练配置"""
    config = (
        PPOConfig()
        .environment(
            env=MultiAGVEnv,
            env_config={
                "num_agvs": ENV_CONFIG.NUM_AGVS,
                "num_tasks": ENV_CONFIG.NUM_TASKS,
                "max_episode_steps": ENV_CONFIG.MAX_EPISODE_STEPS
            }
        )
        .framework("torch")
        .training(
            lr=MAPPO_CONFIG.LEARNING_RATE,
            train_batch_size=MAPPO_CONFIG.BATCH_SIZE,
            sgd_minibatch_size=512,
            num_sgd_iter=10,
            clip_param=MAPPO_CONFIG.CLIP_EPSILON,
            gamma=MAPPO_CONFIG.DISCOUNT_FACTOR,
            lambda_=MAPPO_CONFIG.GAE_LAMBDA,
            entropy_coeff=MAPPO_CONFIG.ENTROPY_COEFF,
            vf_loss_coeff=MAPPO_CONFIG.VALUE_COEFF,
            model={
                "custom_model": "attention_mappo_model",
                "custom_model_config": {}
            }
        )
        .multi_agent(
            policies={
                "shared_policy": (None, None, None, {})
            },
            policy_mapping_fn=lambda agent_id, episode, worker, **kwargs: "shared_policy"
        )
        .rollouts(
            num_rollout_workers=MAPPO_CONFIG.PARALLEL_ENVS,
            rollout_fragment_length=200
        )
        .resources(
            num_gpus=1 if torch.cuda.is_available() else 0
        )
    )
    
    return config


class CurriculumTrainer:
    """课程学习训练器"""
    
    def __init__(self):
        self.curriculum_manager = CurriculumLearningManager()
        self.algorithm = None
        self.training_stats = []
        
    def setup_training(self):
        """设置训练环境"""
        # 注册自定义模型
        ModelCatalog.register_custom_model("attention_mappo_model", AttentionMAPPOModel)
        
        # 初始化Ray
        if not ray.is_initialized():
            ray.init()
        
        # 创建训练配置
        config = create_training_config()
        
        # 创建算法实例
        self.algorithm = config.build()
        
    def train(self):
        """执行训练"""
        print("开始基于单层AGV-Task-AGV注意力机制的MAPPO训练...")
        
        for episode in range(MAPPO_CONFIG.TOTAL_EPISODES):
            # 训练一个iteration
            result = self.algorithm.train()
            
            # 提取性能指标
            performance_metrics = self._extract_performance_metrics(result)
            
            # 更新课程学习
            stage_changed = self.curriculum_manager.update_episode(episode, performance_metrics)
            
            if stage_changed:
                # 阶段变化时调整训练参数
                self._adjust_training_parameters()
            
            # 记录训练统计
            self._log_training_progress(episode, result, performance_metrics)
            
            # 保存模型
            if episode % MAPPO_CONFIG.MODEL_SAVE_INTERVAL == 0:
                self._save_model(episode)
            
            # 早停检查
            if self._should_early_stop(performance_metrics):
                print(f"训练在Episode {episode}达到收敛，提前停止")
                break
        
        print("训练完成！")
        
    def _extract_performance_metrics(self, result: Dict[str, Any]) -> Dict[str, float]:
        """提取性能指标"""
        # 从训练结果中提取关键指标
        metrics = {}
        
        # 基础指标
        if 'episode_reward_mean' in result:
            metrics['average_reward'] = result['episode_reward_mean']
        
        if 'episode_len_mean' in result:
            metrics['average_episode_length'] = result['episode_len_mean']
        
        # 自定义指标（需要从环境中获取）
        custom_metrics = result.get('custom_metrics', {})
        
        if 'task_completion_rate_mean' in custom_metrics:
            metrics['task_completion_rate'] = custom_metrics['task_completion_rate_mean']
        
        if 'load_utilization_mean' in custom_metrics:
            metrics['load_utilization'] = custom_metrics['load_utilization_mean']
        
        if 'collision_rate_mean' in custom_metrics:
            metrics['collision_rate'] = custom_metrics['collision_rate_mean']
        
        if 'cooperation_success_rate_mean' in custom_metrics:
            metrics['cooperation_success_rate'] = custom_metrics['cooperation_success_rate_mean']
        
        if 'path_efficiency_mean' in custom_metrics:
            metrics['path_efficiency'] = custom_metrics['path_efficiency_mean']
        
        return metrics
    
    def _adjust_training_parameters(self):
        """调整训练参数"""
        # 获取自适应学习率
        current_lr = self.algorithm.get_policy().config['lr']
        new_lr = self.curriculum_manager.get_adaptive_learning_rate(current_lr)
        
        # 更新学习率
        self.algorithm.get_policy().config['lr'] = new_lr
        
        # 获取奖励权重调整
        reward_adjustments = self.curriculum_manager.get_reward_weights_adjustment()
        
        # 应用奖励权重调整（需要传递给环境）
        if reward_adjustments:
            print(f"应用奖励权重调整: {reward_adjustments}")
    
    def _log_training_progress(self, episode: int, result: Dict[str, Any], 
                             performance_metrics: Dict[str, float]):
        """记录训练进度"""
        # 记录统计信息
        stats = {
            'episode': episode,
            'stage': self.curriculum_manager.current_stage.name,
            'performance_metrics': performance_metrics,
            'training_time': result.get('time_total_s', 0)
        }
        
        self.training_stats.append(stats)
        
        # 打印进度
        if episode % 100 == 0:
            print(f"Episode {episode}:")
            print(f"  当前阶段: {self.curriculum_manager.current_stage.name}")
            print(f"  平均奖励: {performance_metrics.get('average_reward', 0):.2f}")
            print(f"  任务完成率: {performance_metrics.get('task_completion_rate', 0):.2%}")
            print(f"  载重利用率: {performance_metrics.get('load_utilization', 0):.2%}")
            print(f"  碰撞率: {performance_metrics.get('collision_rate', 0):.2%}")
    
    def _save_model(self, episode: int):
        """保存模型"""
        checkpoint_path = f"checkpoints/mappo_attention_episode_{episode}"
        self.algorithm.save(checkpoint_path)
        print(f"模型已保存到: {checkpoint_path}")
    
    def _should_early_stop(self, performance_metrics: Dict[str, float]) -> bool:
        """检查是否应该早停"""
        # 简单的早停策略：如果最近100个episode的性能稳定且优秀
        if len(self.training_stats) < 100:
            return False
        
        recent_rewards = [stats['performance_metrics'].get('average_reward', 0) 
                         for stats in self.training_stats[-100:]]
        
        # 如果平均奖励高且稳定
        mean_reward = np.mean(recent_rewards)
        std_reward = np.std(recent_rewards)
        
        if mean_reward > 100 and std_reward < 10:  # 根据实际情况调整阈值
            return True
        
        return False


def main():
    """主函数"""
    # 创建检查点目录
    os.makedirs("checkpoints", exist_ok=True)
    
    # 创建训练器
    trainer = CurriculumTrainer()
    
    # 设置训练环境
    trainer.setup_training()
    
    # 开始训练
    trainer.train()
    
    # 清理资源
    ray.shutdown()


if __name__ == "__main__":
    main()
