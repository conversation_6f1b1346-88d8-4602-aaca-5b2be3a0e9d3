"""
路径-载重平衡管理器 - 基于单层AGV-Task-AGV注意力机制的MAPPO多AGV调度优化系统
实现路径效率与载重利用率的权衡优化
"""

import numpy as np
from typing import List, Dict, Optional, Tuple, Set
from dataclasses import dataclass
from utils.config import ENV_CONFIG, REWARD_CONFIG


@dataclass
class PathLoadCandidate:
    """路径-载重候选方案"""
    task_ids: List[int]
    total_weight: int
    path_length: int
    load_utilization: float
    path_efficiency: float
    pareto_score: float
    balance_index: float


@dataclass
class PathLoadBalance:
    """路径-载重平衡结果"""
    optimal_candidates: List[PathLoadCandidate]
    pareto_front: List[PathLoadCandidate]
    recommended_solution: Optional[PathLoadCandidate]
    balance_metrics: Dict[str, float]


class MultiObjectiveOptimizer:
    """多目标优化器"""
    
    def optimize_task_selection(self, agv, available_tasks: List, 
                              unload_zones: List[Tuple[int, int]]) -> List[PathLoadCandidate]:
        """多目标任务选择优化
        
        Args:
            agv: 当前AGV
            available_tasks: 可用任务列表
            unload_zones: 卸货区域列表
            
        Returns:
            优化候选方案列表
        """
        candidates = []
        
        # 生成所有可行的任务组合
        feasible_combinations = self._generate_task_combinations(agv, available_tasks)
        
        for combination in feasible_combinations:
            # 计算路径长度
            path_length = self._calculate_path_length(agv, combination, unload_zones)
            
            # 计算载重利用率
            load_utilization = sum(task.weight for task in combination) / agv.max_capacity
            
            # 计算路径效率
            path_efficiency = self._calculate_path_efficiency(path_length)
            
            # 计算Pareto评分
            pareto_score = self._calculate_pareto_score(load_utilization, path_efficiency)
            
            # 计算平衡指数
            balance_index = self._calculate_balance_index(load_utilization, path_efficiency)
            
            candidate = PathLoadCandidate(
                task_ids=[task.id for task in combination],
                total_weight=sum(task.weight for task in combination),
                path_length=path_length,
                load_utilization=load_utilization,
                path_efficiency=path_efficiency,
                pareto_score=pareto_score,
                balance_index=balance_index
            )
            
            candidates.append(candidate)
        
        # 按Pareto评分排序
        candidates.sort(key=lambda x: x.pareto_score, reverse=True)
        
        return candidates
    
    def _generate_task_combinations(self, agv, available_tasks: List) -> List[List]:
        """生成任务组合"""
        combinations = []
        
        # 过滤可用任务
        valid_tasks = [task for task in available_tasks 
                      if hasattr(task, 'status') and task.status.name == 'AVAILABLE']
        
        # 单任务组合
        for task in valid_tasks:
            if agv.get_remaining_capacity() >= task.weight:
                combinations.append([task])
        
        # 双任务组合
        for i, task1 in enumerate(valid_tasks):
            for task2 in valid_tasks[i+1:]:
                total_weight = task1.weight + task2.weight
                if agv.get_remaining_capacity() >= total_weight:
                    combinations.append([task1, task2])
        
        # 三任务组合（如果载重允许）
        if agv.max_capacity >= 15:  # 只有在载重足够大时才考虑三任务
            for i, task1 in enumerate(valid_tasks):
                for j, task2 in enumerate(valid_tasks[i+1:], i+1):
                    for task3 in valid_tasks[j+1:]:
                        total_weight = task1.weight + task2.weight + task3.weight
                        if agv.get_remaining_capacity() >= total_weight:
                            combinations.append([task1, task2, task3])
        
        return combinations
    
    def _calculate_path_length(self, agv, task_combination: List, 
                             unload_zones: List[Tuple[int, int]]) -> int:
        """计算路径长度（曼哈顿距离）"""
        if not task_combination:
            return 0
        
        current_pos = agv.position
        total_distance = 0
        
        # 到第一个任务的距离
        first_task_pos = task_combination[0].position
        total_distance += abs(current_pos[0] - first_task_pos[0]) + \
                         abs(current_pos[1] - first_task_pos[1])
        
        # 任务间的距离
        for i in range(1, len(task_combination)):
            prev_pos = task_combination[i-1].position
            curr_pos = task_combination[i].position
            total_distance += abs(prev_pos[0] - curr_pos[0]) + abs(prev_pos[1] - curr_pos[1])
        
        # 到最近卸货区的距离
        last_pos = task_combination[-1].position
        min_unload_distance = float('inf')
        for unload_pos in unload_zones:
            distance = abs(last_pos[0] - unload_pos[0]) + abs(last_pos[1] - unload_pos[1])
            min_unload_distance = min(min_unload_distance, distance)
        
        total_distance += min_unload_distance
        
        return total_distance
    
    def _calculate_path_efficiency(self, path_length: int) -> float:
        """计算路径效率"""
        max_path = ENV_CONFIG.MAP_WIDTH + ENV_CONFIG.MAP_HEIGHT
        return max(0, 1.0 - path_length / (max_path * 2))
    
    def _calculate_pareto_score(self, load_utilization: float, path_efficiency: float) -> float:
        """计算Pareto评分"""
        # 使用加权几何平均数，避免某一目标过低
        return (load_utilization ** 0.6) * (path_efficiency ** 0.4)
    
    def _calculate_balance_index(self, load_utilization: float, path_efficiency: float) -> float:
        """计算平衡指数"""
        # 平衡指数：两个目标越接近越好
        diff = abs(load_utilization - path_efficiency)
        return max(0, 1.0 - diff)


class PathLoadBalanceManager:
    """路径-载重平衡管理器
    
    实现研究方案中的路径-载重权衡机制：
    1. 权衡策略：在载重利用率和路径长度间寻找最优平衡点
    2. 多目标优化：同时考虑载重效益和路径成本
    3. 动态权衡：根据系统状态动态调整权衡参数
    """
    
    def __init__(self):
        """初始化路径-载重平衡管理器"""
        self.multi_objective_optimizer = MultiObjectiveOptimizer()
        self.balance_history = []
        self.weight_load = 0.6  # 载重权重
        self.weight_path = 0.4  # 路径权重
        
    def optimize_path_load_balance(self, agvs: List, available_tasks: List,
                                 unload_zones: List[Tuple[int, int]]) -> Dict[str, any]:
        """优化路径-载重平衡
        
        Args:
            agvs: AGV列表
            available_tasks: 可用任务列表
            unload_zones: 卸货区域列表
            
        Returns:
            平衡优化结果
        """
        if not agvs or not available_tasks:
            return {'balance_results': {}, 'system_balance_score': 0.0}
        
        balance_results = {}
        system_scores = []
        
        for agv in agvs:
            # 为每个AGV生成优化候选方案
            candidates = self.multi_objective_optimizer.optimize_task_selection(
                agv, available_tasks, unload_zones
            )
            
            if candidates:
                # 选择最佳平衡方案
                best_candidate = self._select_best_balanced_candidate(candidates)
                
                balance_results[agv.id] = {
                    'best_candidate': best_candidate,
                    'all_candidates': candidates[:5],  # 只保留前5个候选
                    'pareto_front': self._extract_pareto_front(candidates)
                }
                
                system_scores.append(best_candidate.balance_index if best_candidate else 0.0)
            else:
                balance_results[agv.id] = {
                    'best_candidate': None,
                    'all_candidates': [],
                    'pareto_front': []
                }
                system_scores.append(0.0)
        
        # 计算系统整体平衡评分
        system_balance_score = np.mean(system_scores) if system_scores else 0.0
        
        return {
            'balance_results': balance_results,
            'system_balance_score': system_balance_score,
            'balance_metrics': self._calculate_system_balance_metrics(balance_results),
            'optimization_recommendations': self._generate_optimization_recommendations(balance_results)
        }
    
    def _select_best_balanced_candidate(self, candidates: List[PathLoadCandidate]) -> Optional[PathLoadCandidate]:
        """选择最佳平衡候选方案"""
        if not candidates:
            return None
        
        # 动态调整权重
        self._adjust_balance_weights()
        
        best_candidate = None
        best_score = -1
        
        for candidate in candidates:
            # 综合评分：平衡指数 + 加权目标函数
            weighted_score = (
                self.weight_load * candidate.load_utilization +
                self.weight_path * candidate.path_efficiency
            )
            
            # 最终评分：70%加权目标 + 30%平衡指数
            final_score = 0.7 * weighted_score + 0.3 * candidate.balance_index
            
            if final_score > best_score:
                best_score = final_score
                best_candidate = candidate
        
        return best_candidate
    
    def _adjust_balance_weights(self):
        """动态调整平衡权重"""
        if len(self.balance_history) < 10:
            return
        
        # 分析最近的平衡表现
        recent_records = self.balance_history[-10:]
        avg_load_util = np.mean([r['load_utilization'] for r in recent_records])
        avg_path_eff = np.mean([r['path_efficiency'] for r in recent_records])
        
        # 如果载重利用率过低，增加载重权重
        if avg_load_util < 0.6:
            self.weight_load = min(0.8, self.weight_load + 0.05)
            self.weight_path = 1.0 - self.weight_load
        
        # 如果路径效率过低，增加路径权重
        elif avg_path_eff < 0.6:
            self.weight_path = min(0.6, self.weight_path + 0.05)
            self.weight_load = 1.0 - self.weight_path
        
        # 否则逐渐回归默认权重
        else:
            self.weight_load = 0.9 * self.weight_load + 0.1 * 0.6
            self.weight_path = 1.0 - self.weight_load
    
    def _extract_pareto_front(self, candidates: List[PathLoadCandidate]) -> List[PathLoadCandidate]:
        """提取Pareto前沿"""
        if not candidates:
            return []
        
        pareto_front = []
        
        for candidate in candidates:
            is_dominated = False
            
            for other in candidates:
                if (other.load_utilization >= candidate.load_utilization and
                    other.path_efficiency >= candidate.path_efficiency and
                    (other.load_utilization > candidate.load_utilization or
                     other.path_efficiency > candidate.path_efficiency)):
                    is_dominated = True
                    break
            
            if not is_dominated:
                pareto_front.append(candidate)
        
        return pareto_front
    
    def _calculate_system_balance_metrics(self, balance_results: Dict) -> Dict[str, float]:
        """计算系统平衡指标"""
        if not balance_results:
            return {'average_balance': 0.0, 'balance_std': 0.0, 'balance_efficiency': 0.0}
        
        balance_indices = []
        load_utilizations = []
        path_efficiencies = []
        
        for agv_id, result in balance_results.items():
            if result['best_candidate']:
                candidate = result['best_candidate']
                balance_indices.append(candidate.balance_index)
                load_utilizations.append(candidate.load_utilization)
                path_efficiencies.append(candidate.path_efficiency)
        
        if not balance_indices:
            return {'average_balance': 0.0, 'balance_std': 0.0, 'balance_efficiency': 0.0}
        
        return {
            'average_balance': np.mean(balance_indices),
            'balance_std': np.std(balance_indices),
            'average_load_utilization': np.mean(load_utilizations),
            'average_path_efficiency': np.mean(path_efficiencies),
            'balance_efficiency': np.mean(balance_indices) * (1.0 - np.std(balance_indices))
        }
    
    def _generate_optimization_recommendations(self, balance_results: Dict) -> List[str]:
        """生成优化建议"""
        recommendations = []
        
        # 分析系统整体表现
        metrics = self._calculate_system_balance_metrics(balance_results)
        
        if metrics['average_load_utilization'] < 0.6:
            recommendations.append("系统载重利用率偏低，建议增加多任务组合策略")
        
        if metrics['average_path_efficiency'] < 0.6:
            recommendations.append("路径效率偏低，建议优化任务选择顺序")
        
        if metrics['balance_std'] > 0.3:
            recommendations.append("AGV间平衡性差异较大，建议加强协作机制")
        
        if metrics['average_balance'] < 0.5:
            recommendations.append("路径-载重平衡性不佳，建议调整权衡参数")
        
        return recommendations
    
    def get_balance_statistics(self, agvs: List, available_tasks: List,
                             unload_zones: List[Tuple[int, int]]) -> Dict[str, float]:
        """获取路径-载重平衡统计信息"""
        if not self.balance_history:
            return {
                'average_balance_index': 0.0,
                'load_path_correlation': 0.0,
                'optimization_stability': 0.0
            }
        
        recent_records = self.balance_history[-50:]  # 最近50条记录
        
        balance_indices = [r['balance_index'] for r in recent_records]
        load_utils = [r['load_utilization'] for r in recent_records]
        path_effs = [r['path_efficiency'] for r in recent_records]
        
        # 计算载重和路径的相关性
        correlation = np.corrcoef(load_utils, path_effs)[0, 1] if len(load_utils) > 1 else 0.0
        
        # 计算优化稳定性
        stability = 1.0 - np.std(balance_indices) if balance_indices else 0.0
        
        return {
            'average_balance_index': np.mean(balance_indices),
            'load_path_correlation': correlation,
            'optimization_stability': max(0, stability),
            'current_load_weight': self.weight_load,
            'current_path_weight': self.weight_path,
            'total_optimizations': len(self.balance_history)
        }
    
    def update_balance_history(self, balance_index: float, load_utilization: float, 
                             path_efficiency: float):
        """更新平衡历史记录"""
        record = {
            'balance_index': balance_index,
            'load_utilization': load_utilization,
            'path_efficiency': path_efficiency,
            'timestamp': len(self.balance_history)
        }
        
        self.balance_history.append(record)
        
        # 保持历史记录在合理范围内
        if len(self.balance_history) > 1000:
            self.balance_history = self.balance_history[-1000:]
