"""
载重优化器 - 基于单层AGV-Task-AGV注意力机制的MAPPO多AGV调度优化系统
实现载重利用率最大化和动态负载均衡
"""

import numpy as np
from typing import List, Dict, Optional, Tuple, Set
from dataclasses import dataclass
from utils.config import ENV_CONFIG, REWARD_CONFIG


@dataclass
class LoadOptimizationResult:
    """载重优化结果"""
    agv_assignments: Dict[int, List[int]]  # AGV ID -> 任务ID列表
    total_load_utilization: float
    load_balance_score: float
    optimization_score: float


@dataclass
class LoadBalanceMetrics:
    """负载均衡指标"""
    mean_utilization: float
    std_utilization: float
    balance_index: float  # 0-1，越接近1越均衡
    imbalance_penalty: float


class LoadBalancer:
    """负载均衡器"""
    
    def calculate_system_load_metrics(self, agvs: List) -> Dict[str, float]:
        """计算系统载重指标"""
        if not agvs:
            return {'mean_utilization': 0.0, 'std_utilization': 0.0, 'balance_index': 0.0}
        
        utilizations = []
        for agv in agvs:
            utilization = agv.current_load / agv.max_capacity
            utilizations.append(utilization)
        
        mean_util = np.mean(utilizations)
        std_util = np.std(utilizations)
        
        # 平衡指数：标准差越小越均衡
        balance_index = max(0, 1.0 - std_util) if mean_util > 0 else 0.0
        
        return {
            'mean_utilization': mean_util,
            'std_utilization': std_util,
            'balance_index': balance_index,
            'individual_utilizations': utilizations
        }
    
    def detect_load_imbalance(self, agvs: List) -> Dict[str, any]:
        """检测载重不平衡"""
        metrics = self.calculate_system_load_metrics(agvs)
        
        # 不平衡阈值
        IMBALANCE_THRESHOLD = 0.3  # 标准差超过0.3认为不平衡
        
        is_imbalanced = metrics['std_utilization'] > IMBALANCE_THRESHOLD
        
        # 找出过载和欠载的AGV
        utilizations = metrics['individual_utilizations']
        mean_util = metrics['mean_utilization']
        
        overloaded_agvs = []
        underloaded_agvs = []
        
        for i, util in enumerate(utilizations):
            if util > mean_util + IMBALANCE_THRESHOLD:
                overloaded_agvs.append(i)
            elif util < mean_util - IMBALANCE_THRESHOLD:
                underloaded_agvs.append(i)
        
        return {
            'is_imbalanced': is_imbalanced,
            'imbalance_severity': metrics['std_utilization'],
            'overloaded_agvs': overloaded_agvs,
            'underloaded_agvs': underloaded_agvs,
            'balance_index': metrics['balance_index'],
            'recommendations': self._generate_balance_recommendations(
                overloaded_agvs, underloaded_agvs, agvs
            )
        }
    
    def _generate_balance_recommendations(self, overloaded_agvs: List[int], 
                                        underloaded_agvs: List[int], agvs: List) -> List[str]:
        """生成负载均衡建议"""
        recommendations = []
        
        if overloaded_agvs and underloaded_agvs:
            recommendations.append("建议将部分任务从过载AGV转移到欠载AGV")
            
            for overloaded_id in overloaded_agvs:
                for underloaded_id in underloaded_agvs:
                    recommendations.append(
                        f"考虑将AGV{overloaded_id}的部分任务转移给AGV{underloaded_id}"
                    )
        
        elif overloaded_agvs:
            recommendations.append("存在过载AGV，建议重新分配任务")
        
        elif underloaded_agvs:
            recommendations.append("存在欠载AGV，建议增加任务分配")
        
        return recommendations


class LoadUtilizationMaximizer:
    """载重利用率最大化器
    
    实现研究方案中的载重优化机制：
    1. 多任务组合策略：支持AGV同时选择多个兼容任务
    2. 动态载重平衡：实时监控系统整体载重分布
    3. 载重利用率优化：最大化系统整体载重效率
    """
    
    def __init__(self):
        """初始化载重优化器"""
        self.load_balancer = LoadBalancer()
        self.optimization_history = []
        
    def optimize_global_load_utilization(self, agvs: List, available_tasks: List) -> Dict[str, any]:
        """优化全局载重利用率
        
        Args:
            agvs: AGV列表
            available_tasks: 可用任务列表
            
        Returns:
            优化结果字典
        """
        if not agvs or not available_tasks:
            return {'optimization_score': 0.0, 'assignments': {}}
        
        # 获取当前系统状态
        current_metrics = self.load_balancer.calculate_system_load_metrics(agvs)
        
        # 生成优化方案
        optimization_result = self._generate_optimization_plan(agvs, available_tasks)
        
        # 评估优化效果
        optimization_score = self._evaluate_optimization(optimization_result, current_metrics)
        
        return {
            'optimization_result': optimization_result,
            'optimization_score': optimization_score,
            'current_metrics': current_metrics,
            'improvement_potential': self._calculate_improvement_potential(agvs, available_tasks)
        }
    
    def _generate_optimization_plan(self, agvs: List, available_tasks: List) -> LoadOptimizationResult:
        """生成载重优化方案"""
        # 按载重利用率排序AGV（优先给利用率低的AGV分配任务）
        agv_utilizations = []
        for agv in agvs:
            utilization = agv.current_load / agv.max_capacity
            agv_utilizations.append((agv.id, utilization, agv.get_remaining_capacity()))
        
        # 按利用率升序排序
        agv_utilizations.sort(key=lambda x: x[1])
        
        # 按重量降序排序任务（优先分配重任务）
        sorted_tasks = sorted(available_tasks, key=lambda t: t.weight, reverse=True)
        
        # 贪心分配策略
        assignments = {agv.id: [] for agv in agvs}
        total_assigned_weight = 0
        
        for task in sorted_tasks:
            if hasattr(task, 'status') and task.status.name != 'AVAILABLE':
                continue
                
            # 找到最适合的AGV
            best_agv_id = None
            best_score = -1
            
            for agv_id, current_util, remaining_capacity in agv_utilizations:
                if remaining_capacity >= task.weight:
                    # 计算分配后的利用率
                    agv = next(a for a in agvs if a.id == agv_id)
                    new_util = (agv.current_load + task.weight) / agv.max_capacity
                    
                    # 评分：优先提高利用率低的AGV
                    score = (1.0 - current_util) * new_util
                    
                    if score > best_score:
                        best_score = score
                        best_agv_id = agv_id
            
            # 分配任务
            if best_agv_id is not None:
                assignments[best_agv_id].append(task.id)
                total_assigned_weight += task.weight
                
                # 更新AGV利用率信息
                for i, (agv_id, util, capacity) in enumerate(agv_utilizations):
                    if agv_id == best_agv_id:
                        agv = next(a for a in agvs if a.id == agv_id)
                        new_util = (agv.current_load + task.weight) / agv.max_capacity
                        new_capacity = capacity - task.weight
                        agv_utilizations[i] = (agv_id, new_util, new_capacity)
                        break
                
                # 重新排序
                agv_utilizations.sort(key=lambda x: x[1])
        
        # 计算优化指标
        total_capacity = sum(agv.max_capacity for agv in agvs)
        total_load_utilization = total_assigned_weight / total_capacity if total_capacity > 0 else 0
        
        # 计算负载均衡评分
        final_utilizations = [util for _, util, _ in agv_utilizations]
        load_balance_score = 1.0 - np.std(final_utilizations) if final_utilizations else 0
        
        optimization_score = 0.7 * total_load_utilization + 0.3 * load_balance_score
        
        return LoadOptimizationResult(
            agv_assignments=assignments,
            total_load_utilization=total_load_utilization,
            load_balance_score=load_balance_score,
            optimization_score=optimization_score
        )
    
    def _evaluate_optimization(self, result: LoadOptimizationResult, 
                             current_metrics: Dict[str, float]) -> float:
        """评估优化效果"""
        # 载重利用率改进
        current_util = current_metrics['mean_utilization']
        util_improvement = result.total_load_utilization - current_util
        
        # 负载均衡改进
        current_balance = current_metrics['balance_index']
        balance_improvement = result.load_balance_score - current_balance
        
        # 综合评分
        optimization_score = (
            0.6 * max(0, util_improvement) +  # 利用率提升
            0.4 * max(0, balance_improvement)  # 均衡性提升
        )
        
        return optimization_score
    
    def _calculate_improvement_potential(self, agvs: List, available_tasks: List) -> Dict[str, float]:
        """计算改进潜力"""
        if not agvs or not available_tasks:
            return {'utilization_potential': 0.0, 'balance_potential': 0.0}
        
        # 计算理论最大载重利用率
        total_available_weight = sum(task.weight for task in available_tasks 
                                   if hasattr(task, 'status') and task.status.name == 'AVAILABLE')
        total_capacity = sum(agv.max_capacity for agv in agvs)
        max_possible_utilization = min(1.0, total_available_weight / total_capacity)
        
        # 计算当前利用率
        current_metrics = self.load_balancer.calculate_system_load_metrics(agvs)
        current_utilization = current_metrics['mean_utilization']
        
        # 改进潜力
        utilization_potential = max(0, max_possible_utilization - current_utilization)
        balance_potential = max(0, 1.0 - current_metrics['balance_index'])
        
        return {
            'utilization_potential': utilization_potential,
            'balance_potential': balance_potential,
            'max_possible_utilization': max_possible_utilization,
            'current_utilization': current_utilization
        }
    
    def get_optimization_statistics(self, agvs: List, available_tasks: List) -> Dict[str, float]:
        """获取载重优化统计信息"""
        if not agvs:
            return {
                'average_load_utilization': 0.0,
                'load_balance_index': 0.0,
                'optimization_efficiency': 0.0
            }
        
        # 当前系统指标
        metrics = self.load_balancer.calculate_system_load_metrics(agvs)
        
        # 优化效率（基于历史记录）
        optimization_efficiency = 0.0
        if self.optimization_history:
            recent_scores = [record['score'] for record in self.optimization_history[-10:]]
            optimization_efficiency = np.mean(recent_scores)
        
        return {
            'average_load_utilization': metrics['mean_utilization'],
            'load_balance_index': metrics['balance_index'],
            'load_std_deviation': metrics['std_utilization'],
            'optimization_efficiency': optimization_efficiency,
            'total_optimizations': len(self.optimization_history)
        }
    
    def update_optimization_history(self, optimization_score: float, 
                                  load_utilization: float, balance_score: float):
        """更新优化历史记录"""
        record = {
            'score': optimization_score,
            'load_utilization': load_utilization,
            'balance_score': balance_score,
            'timestamp': len(self.optimization_history)
        }
        
        self.optimization_history.append(record)
        
        # 保持历史记录在合理范围内
        if len(self.optimization_history) > 1000:
            self.optimization_history = self.optimization_history[-1000:]
