"""
多目标奖励系统 - 基于单层AGV-Task-AGV注意力机制的MAPPO多AGV调度优化系统
实现研究方案第6章的多目标奖励结构
"""

import numpy as np
from typing import List, Dict, Optional, Tuple, Any
from dataclasses import dataclass
from utils.config import ENV_CONFIG, REWARD_CONFIG


@dataclass
class RewardMetrics:
    """奖励指标"""
    completion_reward: float  # 任务完成奖励
    load_efficiency_reward: float  # 载重效率奖励
    multi_task_reward: float  # 多任务协同奖励
    path_efficiency_reward: float  # 路径效率奖励
    movement_penalty: float  # 移动惩罚
    idle_penalty: float  # 静止惩罚
    balance_reward: float  # 负载均衡奖励
    cooperation_reward: float  # 协作奖励
    collision_penalty: float  # 碰撞惩罚
    overload_penalty: float  # 载重超限惩罚
    invalid_action_penalty: float  # 无效动作惩罚
    total_reward: float  # 总奖励


class MultiObjectiveRewardSystem:
    """多目标奖励系统
    
    实现研究方案中的多目标奖励结构：
    1. 任务完成奖励：基础完成奖励、载重效率奖励、多任务协同奖励
    2. 路径优化奖励：路径效率奖励、移动惩罚、静止惩罚
    3. 协作奖励：负载均衡奖励、协作避让奖励
    4. 约束惩罚：碰撞惩罚、载重超限惩罚、无效动作惩罚
    """
    
    def __init__(self, config: Dict = None):
        """初始化多目标奖励系统"""
        self.config = config or {}
        self.episode_count = 0
        self.reward_history = []
        
        # 奖励权重（可动态调整）
        self.weights = {
            'completion': REWARD_CONFIG.COMPLETION_WEIGHT,
            'load_efficiency': REWARD_CONFIG.LOAD_EFFICIENCY_WEIGHT,
            'multi_task': REWARD_CONFIG.MULTI_TASK_WEIGHT,
            'path_efficiency': REWARD_CONFIG.PATH_EFFICIENCY_WEIGHT,
            'movement_cost': REWARD_CONFIG.MOVEMENT_COST_WEIGHT,
            'idle_penalty': REWARD_CONFIG.IDLE_PENALTY_WEIGHT,
            'load_balance': REWARD_CONFIG.LOAD_BALANCE_WEIGHT,
            'cooperation': REWARD_CONFIG.COOPERATION_WEIGHT,
            'collision_penalty': REWARD_CONFIG.COLLISION_PENALTY_WEIGHT,
            'overload_penalty': REWARD_CONFIG.OVERLOAD_PENALTY_WEIGHT,
            'invalid_action_penalty': REWARD_CONFIG.INVALID_ACTION_PENALTY_WEIGHT
        }
    
    def calculate_total_reward(self, agv, environment_state: Dict[str, Any]) -> Tuple[float, RewardMetrics]:
        """计算总奖励
        
        Args:
            agv: AGV对象
            environment_state: 环境状态信息
            
        Returns:
            (总奖励, 奖励指标详情)
        """
        # 1. 任务完成奖励
        completion_reward = self._calculate_completion_reward(agv, environment_state)
        load_efficiency_reward = self._calculate_load_efficiency_reward(agv, environment_state)
        multi_task_reward = self._calculate_multi_task_reward(agv, environment_state)
        
        # 2. 路径优化奖励
        path_efficiency_reward = self._calculate_path_efficiency_reward(agv, environment_state)
        movement_penalty = self._calculate_movement_penalty(agv, environment_state)
        idle_penalty = self._calculate_idle_penalty(agv, environment_state)
        
        # 3. 协作奖励
        balance_reward = self._calculate_balance_reward(agv, environment_state)
        cooperation_reward = self._calculate_cooperation_reward(agv, environment_state)
        
        # 4. 约束惩罚
        collision_penalty = self._calculate_collision_penalty(agv, environment_state)
        overload_penalty = self._calculate_overload_penalty(agv, environment_state)
        invalid_action_penalty = self._calculate_invalid_action_penalty(agv, environment_state)
        
        # 计算总奖励
        total_reward = (
            completion_reward + load_efficiency_reward + multi_task_reward +
            path_efficiency_reward + movement_penalty + idle_penalty +
            balance_reward + cooperation_reward +
            collision_penalty + overload_penalty + invalid_action_penalty
        )
        
        # 创建奖励指标
        metrics = RewardMetrics(
            completion_reward=completion_reward,
            load_efficiency_reward=load_efficiency_reward,
            multi_task_reward=multi_task_reward,
            path_efficiency_reward=path_efficiency_reward,
            movement_penalty=movement_penalty,
            idle_penalty=idle_penalty,
            balance_reward=balance_reward,
            cooperation_reward=cooperation_reward,
            collision_penalty=collision_penalty,
            overload_penalty=overload_penalty,
            invalid_action_penalty=invalid_action_penalty,
            total_reward=total_reward
        )
        
        # 记录奖励历史
        self._update_reward_history(agv.id, metrics)
        
        return total_reward, metrics
    
    def _calculate_completion_reward(self, agv, environment_state: Dict[str, Any]) -> float:
        """计算任务完成奖励 - R_completion^i"""
        reward = 0.0
        
        # 检查是否有任务刚刚完成
        if hasattr(agv, '_just_completed_tasks'):
            completed_tasks = agv._just_completed_tasks
            for task_weight in completed_tasks:
                reward += task_weight * self.weights['completion']
            agv._just_completed_tasks = []  # 清除标记
        
        return reward
    
    def _calculate_load_efficiency_reward(self, agv, environment_state: Dict[str, Any]) -> float:
        """计算载重效率奖励 - R_load_efficiency^i"""
        if not agv.carrying_tasks:
            return 0.0
        
        # 载重利用率
        load_utilization = agv.current_load / agv.max_capacity
        
        # 只有在完成任务时给予载重效率奖励
        if hasattr(agv, '_just_completed_tasks') and agv._just_completed_tasks:
            reward = self.weights['load_efficiency'] * load_utilization
            return reward
        
        return 0.0
    
    def _calculate_multi_task_reward(self, agv, environment_state: Dict[str, Any]) -> float:
        """计算多任务协同奖励 - R_multi_task^i"""
        if not agv.carrying_tasks or len(agv.carrying_tasks) <= 1:
            return 0.0
        
        # 多任务数量奖励
        task_count_bonus = max(0, len(agv.carrying_tasks) - 1)
        
        # 载重利用率
        load_utilization = agv.current_load / agv.max_capacity
        
        # 只有在完成任务时给予多任务奖励
        if hasattr(agv, '_just_completed_tasks') and agv._just_completed_tasks:
            reward = self.weights['multi_task'] * task_count_bonus * load_utilization
            return reward
        
        return 0.0
    
    def _calculate_path_efficiency_reward(self, agv, environment_state: Dict[str, Any]) -> float:
        """计算路径效率奖励 - R_path^i"""
        # 简化实现：基于AGV的历史移动效率
        if hasattr(agv, 'path_efficiency'):
            efficiency = agv.path_efficiency
            reward = self.weights['path_efficiency'] * (efficiency - 0.5)  # 只奖励高于平均的效率
            return max(0, reward)
        
        return 0.0
    
    def _calculate_movement_penalty(self, agv, environment_state: Dict[str, Any]) -> float:
        """计算移动惩罚 - R_movement^i"""
        # 检查AGV是否移动
        if hasattr(agv, '_just_moved') and agv._just_moved:
            return -self.weights['movement_cost']
        
        return 0.0
    
    def _calculate_idle_penalty(self, agv, environment_state: Dict[str, Any]) -> float:
        """计算静止惩罚 - R_idle^i"""
        # 检查AGV是否无意义等待
        if (agv.status.name == 'IDLE' and 
            not agv.carrying_tasks and 
            hasattr(environment_state, 'task_manager') and
            environment_state['task_manager'].get_available_tasks()):
            return -self.weights['idle_penalty']
        
        return 0.0
    
    def _calculate_balance_reward(self, agv, environment_state: Dict[str, Any]) -> float:
        """计算负载均衡奖励 - R_balance^i"""
        if 'agv_manager' not in environment_state:
            return 0.0
        
        agv_manager = environment_state['agv_manager']
        all_agvs = agv_manager.agvs
        
        if len(all_agvs) <= 1:
            return 0.0
        
        # 计算当前AGV的载重利用率
        current_util = agv.current_load / agv.max_capacity
        
        # 计算系统平均载重利用率
        total_util = sum(other_agv.current_load / other_agv.max_capacity for other_agv in all_agvs)
        avg_util = total_util / len(all_agvs)
        
        # 平衡奖励：利用率越接近平均值越好
        balance_factor = 1.0 - abs(current_util - avg_util)
        reward = self.weights['load_balance'] * balance_factor
        
        return reward
    
    def _calculate_cooperation_reward(self, agv, environment_state: Dict[str, Any]) -> float:
        """计算协作避让奖励 - R_cooperation^i"""
        # 简化实现：检查是否成功避让其他AGV
        if hasattr(agv, '_successful_avoidance_count'):
            avoidance_count = agv._successful_avoidance_count
            reward = self.weights['cooperation'] * avoidance_count
            agv._successful_avoidance_count = 0  # 重置计数
            return reward
        
        return 0.0
    
    def _calculate_collision_penalty(self, agv, environment_state: Dict[str, Any]) -> float:
        """计算碰撞惩罚 - R_collision^i"""
        # 检查是否发生碰撞
        if hasattr(agv, '_just_collided') and agv._just_collided:
            agv._just_collided = False  # 重置标记
            return -self.weights['collision_penalty']
        
        return 0.0
    
    def _calculate_overload_penalty(self, agv, environment_state: Dict[str, Any]) -> float:
        """计算载重超限惩罚 - R_overload^i"""
        if agv.current_load > agv.max_capacity:
            overload_amount = agv.current_load - agv.max_capacity
            return -self.weights['overload_penalty'] * overload_amount
        
        return 0.0
    
    def _calculate_invalid_action_penalty(self, agv, environment_state: Dict[str, Any]) -> float:
        """计算无效动作惩罚 - R_invalid^i"""
        # 检查是否执行了无效动作
        if hasattr(agv, '_invalid_action_count') and agv._invalid_action_count > 0:
            penalty = -self.weights['invalid_action_penalty'] * agv._invalid_action_count
            agv._invalid_action_count = 0  # 重置计数
            return penalty
        
        return 0.0
    
    def _update_reward_history(self, agv_id: int, metrics: RewardMetrics):
        """更新奖励历史记录"""
        record = {
            'agv_id': agv_id,
            'episode': self.episode_count,
            'metrics': metrics
        }
        
        self.reward_history.append(record)
        
        # 保持历史记录在合理范围内
        if len(self.reward_history) > 10000:
            self.reward_history = self.reward_history[-10000:]
    
    def update_episode(self, episode_count: int):
        """更新episode计数"""
        self.episode_count = episode_count
    
    def get_reward_statistics(self) -> Dict[str, float]:
        """获取奖励统计信息"""
        if not self.reward_history:
            return {'average_total_reward': 0.0, 'reward_stability': 0.0}
        
        recent_records = self.reward_history[-100:]  # 最近100条记录
        total_rewards = [record['metrics'].total_reward for record in recent_records]
        
        return {
            'average_total_reward': np.mean(total_rewards),
            'reward_stability': 1.0 - np.std(total_rewards) if len(total_rewards) > 1 else 1.0,
            'max_reward': np.max(total_rewards),
            'min_reward': np.min(total_rewards)
        }
    
    def adjust_weights_for_curriculum(self, stage: int):
        """根据课程学习阶段调整奖励权重"""
        if stage == 1:  # 基础移动学习
            self.weights['completion'] *= 2.0  # 强化任务完成
            self.weights['movement_cost'] *= 0.5  # 减少移动惩罚
        elif stage == 2:  # 单AGV多任务学习
            self.weights['load_efficiency'] *= 1.5  # 强化载重效率
            self.weights['multi_task'] *= 2.0  # 强化多任务奖励
        elif stage >= 3:  # 多AGV协作学习
            self.weights['load_balance'] *= 1.5  # 强化负载均衡
            self.weights['cooperation'] *= 2.0  # 强化协作奖励
