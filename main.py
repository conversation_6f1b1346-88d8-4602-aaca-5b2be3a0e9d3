"""
主入口文件 - 基于单层AGV-Task-AGV注意力机制的MAPPO多AGV调度优化系统
提供统一的入口点来运行训练、评估和演示
"""

import argparse
import os
import sys
from typing import Dict, Any

from train_mappo_attention import CurriculumTrainer
from evaluate_system import SystemEvaluator
from envs.multi_agv_env import MultiAGVEnv
from utils.config import ENV_CONFIG, MAPPO_CONFIG


def run_training(args):
    """运行训练"""
    print("=" * 60)
    print("基于单层AGV-Task-AGV注意力机制的MAPPO多AGV调度优化系统")
    print("开始训练...")
    print("=" * 60)
    
    # 创建训练器
    trainer = CurriculumTrainer()
    
    # 设置训练环境
    trainer.setup_training()
    
    # 开始训练
    trainer.train()
    
    print("训练完成！")


def run_evaluation(args):
    """运行评估"""
    print("=" * 60)
    print("基于单层AGV-Task-AGV注意力机制的MAPPO多AGV调度优化系统")
    print("开始评估...")
    print("=" * 60)
    
    # 创建评估器
    evaluator = SystemEvaluator()
    
    if args.model_path:
        # 评估指定模型
        metrics = evaluator.evaluate_model(args.model_path, args.num_episodes)
        print(f"评估结果:")
        print(f"  任务完成率: {metrics.task_completion_rate:.2%}")
        print(f"  载重利用率: {metrics.load_utilization:.2%}")
        print(f"  路径最优性: {metrics.path_optimality:.2%}")
        print(f"  协作成功率: {metrics.cooperation_success_rate:.2%}")
        print(f"  碰撞率: {metrics.collision_rate:.2%}")
        print(f"  整体性能: {metrics.overall_performance:.3f}")
    else:
        # 运行对比评估
        from evaluate_system import main as eval_main
        eval_main()
    
    print("评估完成！")


def run_demo(args):
    """运行演示"""
    print("=" * 60)
    print("基于单层AGV-Task-AGV注意力机制的MAPPO多AGV调度优化系统")
    print("运行演示...")
    print("=" * 60)
    
    # 创建环境
    env = MultiAGVEnv()
    
    # 运行演示episode
    obs, info = env.reset()
    print("环境已重置")
    
    for step in range(50):  # 运行50步演示
        # 使用随机策略（在实际应用中应该使用训练好的模型）
        actions = {}
        for agent_id in env.agent_ids:
            # 获取动作掩码
            action_mask = env.get_action_mask(agent_id)
            
            # 任务选择动作
            task_mask = action_mask['task_action_mask']
            valid_task_actions = [i for i, valid in enumerate(task_mask) if valid]
            task_action = valid_task_actions[0] if valid_task_actions else 0
            
            # 运动控制动作
            motion_mask = action_mask['motion_action_mask']
            valid_motion_actions = [i for i, valid in enumerate(motion_mask) if valid]
            motion_action = valid_motion_actions[0] if valid_motion_actions else 0
            
            actions[agent_id] = [task_action, motion_action]
        
        # 执行动作
        obs, rewards, terminateds, truncateds, infos = env.step(actions)
        
        # 显示状态
        if step % 10 == 0:
            print(f"\nStep {step}:")
            env.render()
            print(f"奖励: {rewards}")
            
            # 显示系统统计
            if 'agv_0' in infos:
                stats = infos['agv_0'].get('system_stats', {})
                print(f"系统载重利用率: {stats.get('average_load_utilization', 0):.2%}")
                print(f"任务完成率: {infos['agv_0'].get('completion_rate', 0):.2%}")
        
        # 检查终止条件
        if terminateds.get("__all__", False):
            print(f"\n所有任务完成！总步数: {step + 1}")
            break
    
    print("演示完成！")


def run_analysis(args):
    """运行系统分析"""
    print("=" * 60)
    print("基于单层AGV-Task-AGV注意力机制的MAPPO多AGV调度优化系统")
    print("系统分析...")
    print("=" * 60)
    
    # 创建环境进行分析
    env = MultiAGVEnv()
    
    print("环境配置分析:")
    print(f"  地图尺寸: {ENV_CONFIG.MAP_WIDTH} × {ENV_CONFIG.MAP_HEIGHT}")
    print(f"  AGV数量: {ENV_CONFIG.NUM_AGVS}")
    print(f"  任务数量: {ENV_CONFIG.NUM_TASKS}")
    print(f"  最大载重: {ENV_CONFIG.MAX_CAPACITY}")
    print(f"  最大步数: {ENV_CONFIG.MAX_EPISODE_STEPS}")
    
    print("\n观测空间分析:")
    print(f"  AGV状态维度: {env.observation_space.shape[0]}")
    print(f"  动作空间: {env.action_space}")
    
    print("\n系统组件分析:")
    print("  ✓ 地图管理器 (MapManager)")
    print("  ✓ 任务管理器 (TaskManager)")
    print("  ✓ AGV管理器 (AGVManager)")
    print("  ✓ 自主任务选择器 (AutonomousTaskSelector)")
    print("  ✓ 载重优化器 (LoadUtilizationMaximizer)")
    print("  ✓ 路径-载重平衡管理器 (PathLoadBalanceManager)")
    print("  ✓ 多任务携带管理器 (MultiTaskCarryingManager)")
    print("  ✓ 多目标奖励系统 (MultiObjectiveRewardSystem)")
    
    print("\n核心创新分析:")
    print("  ✓ 单层AGV-Task-AGV注意力机制")
    print("  ✓ 约束增强异构图注意力")
    print("  ✓ MAPPO深度融合")
    print("  ✓ 渐进式课程学习（6阶段）")
    print("  ✓ Top-K稀疏化优化")
    
    print("\n训练配置分析:")
    print(f"  学习率: {MAPPO_CONFIG.LEARNING_RATE}")
    print(f"  批次大小: {MAPPO_CONFIG.BATCH_SIZE}")
    print(f"  总训练episodes: {MAPPO_CONFIG.TOTAL_EPISODES}")
    print(f"  并行环境数: {MAPPO_CONFIG.PARALLEL_ENVS}")
    
    print("系统分析完成！")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="基于单层AGV-Task-AGV注意力机制的MAPPO多AGV调度优化系统",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python main.py train                    # 开始训练
  python main.py eval                     # 运行评估对比
  python main.py eval --model checkpoints/model.pkl --episodes 200  # 评估指定模型
  python main.py demo                     # 运行演示
  python main.py analysis                 # 系统分析
        """
    )
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 训练命令
    train_parser = subparsers.add_parser('train', help='开始训练')
    train_parser.add_argument('--resume', type=str, help='从检查点恢复训练')
    
    # 评估命令
    eval_parser = subparsers.add_parser('eval', help='评估模型')
    eval_parser.add_argument('--model', dest='model_path', type=str, help='模型路径')
    eval_parser.add_argument('--episodes', dest='num_episodes', type=int, default=100, help='评估episode数量')
    
    # 演示命令
    demo_parser = subparsers.add_parser('demo', help='运行演示')
    demo_parser.add_argument('--steps', type=int, default=50, help='演示步数')
    
    # 分析命令
    analysis_parser = subparsers.add_parser('analysis', help='系统分析')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    try:
        if args.command == 'train':
            run_training(args)
        elif args.command == 'eval':
            run_evaluation(args)
        elif args.command == 'demo':
            run_demo(args)
        elif args.command == 'analysis':
            run_analysis(args)
        else:
            parser.print_help()
    
    except KeyboardInterrupt:
        print("\n用户中断操作")
    except Exception as e:
        print(f"执行过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
