"""
配置文件 - 基于单层AGV-Task-AGV注意力机制的MAPPO多AGV调度优化系统
包含所有系统配置参数，严格按照研究方案设计
"""

import numpy as np
from dataclasses import dataclass
from typing import Dict, Tuple, List


@dataclass
class EnvironmentConfig:
    """环境配置 - 2.2.1 物理环境配置"""
    # 地图规格
    MAP_WIDTH: int = 26  # 网格宽度
    MAP_HEIGHT: int = 10  # 网格高度
    
    # AGV配置
    NUM_AGVS: int = 4  # AGV数量，编号0-3
    MAX_CAPACITY: int = 25  # 每个AGV最大载重25单位
    
    # 任务配置
    NUM_TASKS: int = 16  # 任务数量，分布在16个货架位置
    TASK_WEIGHTS: List[int] = [5, 10]  # 任务重量：5或10单位
    
    # 货架和卸货点配置
    NUM_SHELVES: int = 16  # 货架数量
    NUM_UNLOAD_ZONES: int = 2  # 卸货点数量
    UNLOAD_POSITIONS: List[Tuple[int, int]] = [(0, 0), (25, 0)]  # 左上角和右上角
    
    # Episode配置
    MAX_EPISODE_STEPS: int = 500  # 每个episode最大步数


@dataclass
class StateConfig:
    """状态空间配置 - 2.2.2 状态空间设计"""
    # AGV局部观测（5维）
    AGV_STATE_DIM: int = 5
    # - 当前位置归一化：x_norm, y_norm ∈ [0,1]
    # - 当前载重比例：load_ratio ∈ [0,1]
    # - 目标卸货点：target_unload ∈ {0,1}
    # - 携带任务数量归一化：task_count ∈ [0,1]
    
    # 任务全局状态（每个任务4维，共16×4=64维）
    TASK_STATE_DIM: int = 4
    TOTAL_TASK_STATE_DIM: int = 16 * 4  # 64维
    # - 任务位置归一化：x_task, y_task ∈ [0,1]
    # - 任务重量归一化：weight_norm ∈ [0,1]
    # - 任务状态编码：status ∈ {0, 0.33, 0.67, 1}
    # - 目标卸货点：target ∈ {0,1}
    
    # 环境全局状态（8维）
    GLOBAL_STATE_DIM: int = 8
    # - 已完成任务数归一化、活跃任务数归一化、平均载重利用率、碰撞次数归一化
    # - 时间步进度、系统拥堵程度、整体系统效率、路径最优性评分
    
    # 总观测维度
    TOTAL_OBS_DIM: int = AGV_STATE_DIM + TOTAL_TASK_STATE_DIM + GLOBAL_STATE_DIM  # 77维


@dataclass
class ActionConfig:
    """动作空间配置 - 2.2.3 动作空间设计"""
    # 任务选择动作（18维）
    TASK_ACTION_DIM: int = 18
    SELECT_TASK_START: int = 0  # 选择任务0-15
    SELECT_TASK_END: int = 15
    KEEP_CURRENT: int = 16  # 保持当前任务组合
    GO_TO_UNLOAD: int = 17  # 前往卸货区域
    
    # 运动控制动作（5维）
    MOTION_ACTION_DIM: int = 5
    UP: int = 0
    DOWN: int = 1
    LEFT: int = 2
    RIGHT: int = 3
    WAIT: int = 4
    
    # 运动向量映射
    MOTION_VECTORS: Dict[int, Tuple[int, int]] = {
        UP: (0, -1),
        DOWN: (0, 1),
        LEFT: (-1, 0),
        RIGHT: (1, 0),
        WAIT: (0, 0)
    }


@dataclass
class RewardConfig:
    """奖励函数配置 - 第6章 奖励函数设计"""
    # 任务完成奖励权重
    COMPLETION_WEIGHT: float = 100.0  # β_completion
    LOAD_EFFICIENCY_WEIGHT: float = 50.0  # β_load
    MULTI_TASK_WEIGHT: float = 20.0  # β_multi
    
    # 路径优化奖励权重
    PATH_EFFICIENCY_WEIGHT: float = 10.0  # β_path
    MOVEMENT_COST_WEIGHT: float = 0.1  # β_move
    IDLE_PENALTY_WEIGHT: float = 1.0  # β_idle
    
    # 协作奖励权重
    LOAD_BALANCE_WEIGHT: float = 15.0  # β_balance
    COOPERATION_WEIGHT: float = 5.0  # β_coop
    
    # 约束惩罚权重
    COLLISION_PENALTY_WEIGHT: float = 50.0  # β_collision
    OVERLOAD_PENALTY_WEIGHT: float = 100.0  # β_overload
    INVALID_ACTION_PENALTY_WEIGHT: float = 10.0  # β_invalid


@dataclass
class AttentionConfig:
    """注意力机制配置 - 第4章 单层AGV-Task-AGV注意力机制设计"""
    # 注意力维度
    ATTENTION_DIM: int = 64  # d_k
    HIDDEN_DIM: int = 64  # 隐藏层维度
    
    # 稀疏化参数
    TOP_K_TASKS: int = 8  # 任务分配稀疏化，只关注得分最高的K个任务
    TOP_K_AGVS: int = 3  # 协作感知稀疏化，只关注距离最近的K个AGV
    
    # 约束权重参数
    DISTANCE_WEIGHT: float = 1.0  # λ_d
    LOAD_WEIGHT: float = 2.0  # λ_l
    PATH_WEIGHT: float = 1.5  # λ_p
    BALANCE_WEIGHT: float = 1.0  # λ_bal
    NEAR_WEIGHT: float = 2.0  # λ_near
    MID_WEIGHT: float = 1.0  # λ_mid
    FAR_WEIGHT: float = 0.5  # λ_far
    COOPERATION_WEIGHT: float = 1.5  # λ_coop


@dataclass
class MAPPOConfig:
    """MAPPO算法配置 - 第5章 MAPPO算法集成"""
    # 网络结构
    FEATURE_EXTRACT_DIM: int = 64  # 特征提取维度
    POLICY_HIDDEN_DIM: int = 64  # 策略网络隐藏层维度
    VALUE_HIDDEN_DIM: int = 64  # 价值网络隐藏层维度
    
    # PPO参数
    LEARNING_RATE: float = 3e-4  # 学习率
    BATCH_SIZE: int = 2048  # 批次大小
    CLIP_EPSILON: float = 0.2  # PPO剪切参数
    DISCOUNT_FACTOR: float = 0.99  # 折扣因子γ
    GAE_LAMBDA: float = 0.95  # GAE参数λ
    ENTROPY_COEFF: float = 0.01  # 熵系数
    VALUE_COEFF: float = 0.5  # 价值函数系数
    
    # 训练参数
    TOTAL_EPISODES: int = 10000  # 总训练episodes
    PARALLEL_ENVS: int = 16  # 并行环境数
    MODEL_SAVE_INTERVAL: int = 500  # 模型保存间隔
    
    # 损失函数权重
    SPARSE_LOSS_WEIGHT: float = 0.01  # λ_sparse
    SMOOTH_LOSS_WEIGHT: float = 0.01  # λ_smooth


@dataclass
class CurriculumConfig:
    """课程学习配置 - 第7章 渐进式课程学习"""
    # 阶段1：基础移动学习（Episode 0-500）
    STAGE1_EPISODES: int = 500
    STAGE1_NUM_AGVS: int = 1
    STAGE1_NUM_TASKS: int = 4
    STAGE1_TASK_WEIGHT: int = 5  # 固定重量
    STAGE1_SUCCESS_CRITERIA: Dict[str, float] = {
        'task_completion_rate': 0.8,
        'path_efficiency': 0.7,
        'collision_rate': 0.05
    }
    
    # 阶段2：单AGV多任务学习（Episode 500-1500）
    STAGE2_EPISODES: int = 1500
    STAGE2_NUM_AGVS: int = 1
    STAGE2_NUM_TASKS: int = 8
    STAGE2_SUCCESS_CRITERIA: Dict[str, float] = {
        'load_utilization': 0.6,
        'multi_task_completion_rate': 0.5
    }
    
    # 阶段3：双AGV协作学习（Episode 1500-3000）
    STAGE3_EPISODES: int = 3000
    STAGE3_NUM_AGVS: int = 2
    STAGE3_NUM_TASKS: int = 12
    STAGE3_SUCCESS_CRITERIA: Dict[str, float] = {
        'cooperation_success_rate': 0.7,
        'collision_rate': 0.03,
        'load_balance': 0.6
    }
    
    # 阶段4：多AGV复杂协作（Episode 3000-5000）
    STAGE4_EPISODES: int = 5000
    STAGE4_NUM_AGVS: int = 3
    STAGE4_NUM_TASKS: int = 14
    STAGE4_SUCCESS_CRITERIA: Dict[str, float] = {
        'system_task_completion_rate': 0.65,
        'load_utilization': 0.7,
        'cooperation_efficiency': 0.75
    }
    
    # 阶段5：满载协作优化（Episode 5000-7500）
    STAGE5_EPISODES: int = 7500
    STAGE5_NUM_AGVS: int = 4
    STAGE5_NUM_TASKS: int = 16
    STAGE5_SUCCESS_CRITERIA: Dict[str, float] = {
        'system_task_completion_rate': 0.7,
        'load_utilization': 0.8,
        'path_efficiency': 0.85,
        'collision_rate': 0.02
    }
    
    # 阶段6：动态环境适应（Episode 7500-10000）
    STAGE6_EPISODES: int = 10000
    STAGE6_SUCCESS_CRITERIA: Dict[str, float] = {
        'dynamic_adaptation': 0.8,
        'robustness': 0.75
    }


# 全局配置实例
ENV_CONFIG = EnvironmentConfig()
STATE_CONFIG = StateConfig()
ACTION_CONFIG = ActionConfig()
REWARD_CONFIG = RewardConfig()
ATTENTION_CONFIG = AttentionConfig()
MAPPO_CONFIG = MAPPOConfig()
CURRICULUM_CONFIG = CurriculumConfig()


# 地图布局配置
class MapLayout:
    """地图布局配置 - 26×10网格世界"""
    
    @staticmethod
    def get_shelf_positions() -> List[Tuple[int, int]]:
        """获取16个货架位置"""
        # 按照仓储布局设计，货架分布在地图中央区域
        shelf_positions = []
        
        # 第一排货架 (y=2)
        for x in [3, 6, 9, 12, 15, 18, 21, 24]:
            shelf_positions.append((x, 2))
        
        # 第二排货架 (y=7)
        for x in [3, 6, 9, 12, 15, 18, 21, 24]:
            shelf_positions.append((x, 7))
        
        return shelf_positions
    
    @staticmethod
    def get_agv_initial_positions() -> List[Tuple[int, int]]:
        """获取4个AGV初始位置"""
        return [
            (1, 5),   # AGV 0
            (8, 5),   # AGV 1
            (16, 5),  # AGV 2
            (23, 5)   # AGV 3
        ]
    
    @staticmethod
    def is_walkable(x: int, y: int) -> bool:
        """检查位置是否可通行"""
        # 边界检查
        if x < 0 or x >= ENV_CONFIG.MAP_WIDTH or y < 0 or y >= ENV_CONFIG.MAP_HEIGHT:
            return False
        
        # 货架位置不可通行
        shelf_positions = MapLayout.get_shelf_positions()
        if (x, y) in shelf_positions:
            return False
        
        return True
