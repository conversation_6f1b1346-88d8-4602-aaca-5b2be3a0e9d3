"""
自主任务选择器 - 基于单层AGV-Task-AGV注意力机制的MAPPO多AGV调度优化系统
实现AGV自主任务选择和载重优化，支持多任务组合决策
"""

import numpy as np
from typing import List, Dict, Optional, Tuple, Set
from dataclasses import dataclass
from utils.config import ENV_CONFIG, ACTION_CONFIG, ATTENTION_CONFIG


@dataclass
class TaskCombination:
    """任务组合类"""
    task_ids: List[int]
    total_weight: int
    positions: List[Tuple[int, int]]
    metrics: 'TaskSelectionMetrics'


@dataclass
class TaskSelectionMetrics:
    """任务选择评估指标"""
    load_utilization: float  # 载重利用率
    path_efficiency: float   # 路径效率
    collaboration_score: float  # 协作评分
    total_score: float      # 总评分


class AutonomousTaskSelector:
    """自主任务选择器
    
    实现研究方案中的自主任务选择机制：
    1. 载重评估：计算当前载重利用率和剩余容量
    2. 任务筛选：基于载重约束筛选可行任务集合
    3. 组合优化：评估不同任务组合的载重效率和路径成本
    4. 最优选择：通过注意力机制选择最优任务组合
    """
    
    def __init__(self):
        """初始化自主任务选择器"""
        self.selection_history = []
        self.success_count = 0
        self.total_selections = 0
        
    def get_action_mask(self, agv, all_tasks: List, other_agvs: List, 
                       unload_zones: List[Tuple[int, int]]) -> np.ndarray:
        """生成智能任务选择动作掩码
        
        Args:
            agv: 当前AGV对象
            all_tasks: 所有任务列表
            other_agvs: 其他AGV列表
            unload_zones: 卸货区域列表
            
        Returns:
            任务动作掩码数组
        """
        mask = np.zeros(ACTION_CONFIG.TASK_ACTION_DIM, dtype=bool)
        
        # 1. 保持当前任务组合总是可用
        mask[ACTION_CONFIG.KEEP_CURRENT] = True
        
        # 2. 如果有携带任务，可以前往卸货区域
        if agv.carrying_tasks:
            mask[ACTION_CONFIG.GO_TO_UNLOAD] = True
        
        # 3. 检查每个任务是否可选择
        for task_id in range(ENV_CONFIG.NUM_TASKS):
            if task_id < len(all_tasks):
                task = all_tasks[task_id]
                
                # 检查任务是否可用
                if hasattr(task, 'status') and task.status.name == 'AVAILABLE':
                    # 检查载重约束
                    if agv.get_remaining_capacity() >= task.weight:
                        # 检查是否与其他AGV冲突
                        if not self._check_task_conflict(task, other_agvs):
                            # 检查路径合理性
                            if self._check_path_feasibility(agv, task, unload_zones):
                                mask[ACTION_CONFIG.SELECT_TASK_START + task_id] = True
        
        return mask
    
    def select_optimal_task_combination(self, agv, all_tasks: List, other_agvs: List,
                                      unload_zones: List[Tuple[int, int]]) -> Optional[TaskCombination]:
        """选择最优任务组合
        
        Args:
            agv: 当前AGV对象
            all_tasks: 所有任务列表
            other_agvs: 其他AGV列表
            unload_zones: 卸货区域列表
            
        Returns:
            最优任务组合（如果存在）
        """
        # 获取可用任务
        available_tasks = [task for task in all_tasks 
                          if hasattr(task, 'status') and task.status.name == 'AVAILABLE']
        
        if not available_tasks:
            return None
        
        # 生成所有可行的任务组合
        feasible_combinations = self._generate_feasible_combinations(
            agv, available_tasks, other_agvs, unload_zones
        )
        
        if not feasible_combinations:
            return None
        
        # 评估每个组合并选择最优
        best_combination = None
        best_score = -float('inf')
        
        for combination in feasible_combinations:
            metrics = self._evaluate_task_combination(agv, combination, other_agvs, unload_zones)
            combination.metrics = metrics
            
            if metrics.total_score > best_score:
                best_score = metrics.total_score
                best_combination = combination
        
        return best_combination
    
    def _check_task_conflict(self, task, other_agvs: List) -> bool:
        """检查任务是否与其他AGV冲突"""
        for other_agv in other_agvs:
            # 检查是否已被其他AGV选择
            if hasattr(other_agv, 'target_task_id') and other_agv.target_task_id == task.id:
                return True
            
            # 检查是否在其他AGV的路径上
            if hasattr(other_agv, 'target_position') and other_agv.target_position == task.position:
                return True
        
        return False
    
    def _check_path_feasibility(self, agv, task, unload_zones: List[Tuple[int, int]]) -> bool:
        """检查路径可行性"""
        # 计算到任务的距离
        task_distance = abs(agv.position[0] - task.position[0]) + abs(agv.position[1] - task.position[1])
        
        # 计算到最近卸货区的距离
        min_unload_distance = float('inf')
        for unload_pos in unload_zones:
            unload_distance = abs(task.position[0] - unload_pos[0]) + abs(task.position[1] - unload_pos[1])
            min_unload_distance = min(min_unload_distance, unload_distance)
        
        # 总路径长度不应过长
        total_path = task_distance + min_unload_distance
        max_reasonable_path = ENV_CONFIG.MAP_WIDTH + ENV_CONFIG.MAP_HEIGHT  # 地图对角线长度
        
        return total_path <= max_reasonable_path * 1.5  # 允许1.5倍的合理绕行
    
    def _generate_feasible_combinations(self, agv, available_tasks: List, other_agvs: List,
                                      unload_zones: List[Tuple[int, int]]) -> List[TaskCombination]:
        """生成所有可行的任务组合"""
        combinations = []
        
        # 单任务组合
        for task in available_tasks:
            if agv.get_remaining_capacity() >= task.weight:
                if not self._check_task_conflict(task, other_agvs):
                    if self._check_path_feasibility(agv, task, unload_zones):
                        combination = TaskCombination(
                            task_ids=[task.id],
                            total_weight=task.weight,
                            positions=[task.position],
                            metrics=None
                        )
                        combinations.append(combination)
        
        # 多任务组合（最多3个任务，避免组合爆炸）
        if len(available_tasks) > 1:
            for i, task1 in enumerate(available_tasks):
                for j, task2 in enumerate(available_tasks[i+1:], i+1):
                    total_weight = task1.weight + task2.weight
                    if agv.get_remaining_capacity() >= total_weight:
                        if (not self._check_task_conflict(task1, other_agvs) and 
                            not self._check_task_conflict(task2, other_agvs)):
                            combination = TaskCombination(
                                task_ids=[task1.id, task2.id],
                                total_weight=total_weight,
                                positions=[task1.position, task2.position],
                                metrics=None
                            )
                            combinations.append(combination)
        
        return combinations
    
    def _evaluate_task_combination(self, agv, combination: TaskCombination, other_agvs: List,
                                 unload_zones: List[Tuple[int, int]]) -> TaskSelectionMetrics:
        """评估任务组合"""
        # 1. 载重利用率
        load_utilization = combination.total_weight / agv.max_capacity
        
        # 2. 路径效率
        path_efficiency = self._calculate_path_efficiency(agv, combination, unload_zones)
        
        # 3. 协作评分
        collaboration_score = self._calculate_collaboration_score(agv, combination, other_agvs)
        
        # 4. 总评分（加权组合）
        total_score = (
            0.4 * load_utilization +
            0.3 * path_efficiency +
            0.3 * collaboration_score
        )
        
        return TaskSelectionMetrics(
            load_utilization=load_utilization,
            path_efficiency=path_efficiency,
            collaboration_score=collaboration_score,
            total_score=total_score
        )
    
    def _calculate_path_efficiency(self, agv, combination: TaskCombination,
                                 unload_zones: List[Tuple[int, int]]) -> float:
        """计算路径效率"""
        if not combination.positions:
            return 0.0
        
        # 计算最优路径长度（简化为曼哈顿距离）
        current_pos = agv.position
        total_distance = 0
        
        # 到第一个任务的距离
        total_distance += abs(current_pos[0] - combination.positions[0][0]) + \
                         abs(current_pos[1] - combination.positions[0][1])
        
        # 任务间的距离
        for i in range(1, len(combination.positions)):
            prev_pos = combination.positions[i-1]
            curr_pos = combination.positions[i]
            total_distance += abs(prev_pos[0] - curr_pos[0]) + abs(prev_pos[1] - curr_pos[1])
        
        # 到最近卸货区的距离
        last_pos = combination.positions[-1]
        min_unload_distance = float('inf')
        for unload_pos in unload_zones:
            distance = abs(last_pos[0] - unload_pos[0]) + abs(last_pos[1] - unload_pos[1])
            min_unload_distance = min(min_unload_distance, distance)
        
        total_distance += min_unload_distance
        
        # 计算效率（距离越短效率越高）
        max_distance = ENV_CONFIG.MAP_WIDTH + ENV_CONFIG.MAP_HEIGHT
        efficiency = max(0, 1.0 - total_distance / (max_distance * 2))
        
        return efficiency
    
    def _calculate_collaboration_score(self, agv, combination: TaskCombination,
                                     other_agvs: List) -> float:
        """计算协作评分"""
        if not other_agvs:
            return 1.0
        
        score = 1.0
        
        # 检查与其他AGV的距离和负载均衡
        agv_load_after = (agv.current_load + combination.total_weight) / agv.max_capacity
        
        for other_agv in other_agvs:
            # 距离因子
            distance = abs(agv.position[0] - other_agv.position[0]) + \
                      abs(agv.position[1] - other_agv.position[1])
            distance_factor = min(1.0, distance / 10.0)  # 距离越远影响越小
            
            # 负载均衡因子
            other_load = other_agv.current_load / other_agv.max_capacity
            load_diff = abs(agv_load_after - other_load)
            balance_factor = max(0, 1.0 - load_diff)  # 负载差异越小越好
            
            # 综合评分
            score *= (0.5 * distance_factor + 0.5 * balance_factor)
        
        return score
    
    def get_selection_statistics(self) -> Dict[str, float]:
        """获取任务选择统计信息"""
        if self.total_selections == 0:
            return {
                'success_rate': 0.0,
                'average_load_utilization': 0.0,
                'total_selections': 0
            }
        
        return {
            'success_rate': self.success_count / self.total_selections,
            'average_load_utilization': 0.0,  # 需要更复杂的统计
            'total_selections': self.total_selections
        }
    
    def update_selection_result(self, success: bool, load_utilization: float):
        """更新选择结果统计"""
        self.total_selections += 1
        if success:
            self.success_count += 1
        
        self.selection_history.append({
            'success': success,
            'load_utilization': load_utilization,
            'timestamp': self.total_selections
        })
        
        # 保持历史记录在合理范围内
        if len(self.selection_history) > 1000:
            self.selection_history = self.selection_history[-1000:]
