"""
地图管理器 - 多载重AGV通信增强调度系统
负责管理26×10网格世界，包括货架布局、通道系统和卸货区域
"""

import numpy as np
from typing import List, Tuple, Set, Optional
from utils.config import ENV_CONFIG


class MapManager:
    """地图管理器类"""
    
    def __init__(self):
        """初始化地图管理器"""
        self.width = ENV_CONFIG.MAP_WIDTH
        self.height = ENV_CONFIG.MAP_HEIGHT
        self.shelf_width = ENV_CONFIG.SHELF_WIDTH
        self.shelf_height = ENV_CONFIG.SHELF_HEIGHT
        self.shelf_rows = ENV_CONFIG.SHELF_ROWS
        self.shelf_cols = ENV_CONFIG.SHELF_COLS
        self.channel_width = ENV_CONFIG.CHANNEL_WIDTH
        
        # 地图网格：0=可通行，1=货架，2=卸货区域
        self.grid = np.zeros((self.height, self.width), dtype=int)
        
        # 货架位置列表
        self.shelf_positions = []
        
        # 卸货区域位置
        self.unload_zones = ENV_CONFIG.UNLOAD_ZONES
        
        # 可通行位置集合
        self.walkable_positions = set()
        
        # 初始化地图
        self._initialize_map()
    
    def _initialize_map(self):
        """初始化地图布局"""
        # 1. 计算货架布局
        self._place_shelves()
        
        # 2. 设置卸货区域
        self._place_unload_zones()
        
        # 3. 计算可通行位置
        self._calculate_walkable_positions()
        
        # 4. 验证地图有效性
        self._validate_map()
    
    def _place_shelves(self):
        """放置货架 - 3行5列分布"""
        # 计算货架起始位置，确保居中分布且有通道
        total_shelf_width = self.shelf_cols * self.shelf_width + (self.shelf_cols - 1) * self.channel_width
        total_shelf_height = self.shelf_rows * self.shelf_height + (self.shelf_rows - 1) * self.channel_width
        
        # 计算起始偏移，使货架区域居中
        start_x = (self.width - total_shelf_width) // 2
        start_y = (self.height - total_shelf_height) // 2
        
        shelf_id = 0
        for row in range(self.shelf_rows):
            for col in range(self.shelf_cols):
                # 计算当前货架的左上角位置
                shelf_x = start_x + col * (self.shelf_width + self.channel_width)
                shelf_y = start_y + row * (self.shelf_height + self.channel_width)
                
                # 在网格中标记货架区域
                for dy in range(self.shelf_height):
                    for dx in range(self.shelf_width):
                        x, y = shelf_x + dx, shelf_y + dy
                        if 0 <= x < self.width and 0 <= y < self.height:
                            self.grid[y, x] = 1  # 标记为货架
                
                # 记录货架位置（中心点）
                center_x = shelf_x + self.shelf_width // 2
                center_y = shelf_y + self.shelf_height // 2
                self.shelf_positions.append((center_x, center_y))
                
                shelf_id += 1
                if shelf_id >= ENV_CONFIG.TOTAL_SHELVES:
                    break
            if shelf_id >= ENV_CONFIG.TOTAL_SHELVES:
                break
    
    def _place_unload_zones(self):
        """设置卸货区域"""
        for x, y in self.unload_zones:
            if 0 <= x < self.width and 0 <= y < self.height:
                self.grid[y, x] = 2  # 标记为卸货区域
    
    def _calculate_walkable_positions(self):
        """计算所有可通行位置"""
        self.walkable_positions.clear()
        for y in range(self.height):
            for x in range(self.width):
                if self.grid[y, x] == 0 or self.grid[y, x] == 2:  # 可通行或卸货区域
                    self.walkable_positions.add((x, y))
    
    def _validate_map(self):
        """验证地图有效性"""
        # 检查货架数量
        assert len(self.shelf_positions) == ENV_CONFIG.TOTAL_SHELVES, \
            f"货架数量不匹配: 期望{ENV_CONFIG.TOTAL_SHELVES}, 实际{len(self.shelf_positions)}"
        
        # 检查卸货区域
        for x, y in self.unload_zones:
            assert self.grid[y, x] == 2, f"卸货区域({x}, {y})未正确设置"
        
        # 检查连通性（简单检查）
        assert len(self.walkable_positions) > 0, "没有可通行位置"
        
        print(f"地图验证通过: {self.width}×{self.height}, {len(self.shelf_positions)}个货架, {len(self.walkable_positions)}个可通行位置")
    
    def is_walkable(self, x: int, y: int) -> bool:
        """检查位置是否可通行"""
        return (x, y) in self.walkable_positions
    
    def is_shelf(self, x: int, y: int) -> bool:
        """检查位置是否为货架"""
        return 0 <= x < self.width and 0 <= y < self.height and self.grid[y, x] == 1
    
    def is_unload_zone(self, x: int, y: int) -> bool:
        """检查位置是否为卸货区域"""
        return 0 <= x < self.width and 0 <= y < self.height and self.grid[y, x] == 2
    
    def get_shelf_positions(self) -> List[Tuple[int, int]]:
        """获取所有货架位置"""
        return self.shelf_positions.copy()
    
    def get_unload_zones(self) -> List[Tuple[int, int]]:
        """获取所有卸货区域位置"""
        return self.unload_zones.copy()
    
    def get_walkable_positions(self) -> Set[Tuple[int, int]]:
        """获取所有可通行位置"""
        return self.walkable_positions.copy()
    
    def get_neighbors(self, x: int, y: int) -> List[Tuple[int, int]]:
        """获取位置的可通行邻居"""
        neighbors = []
        for dx, dy in [(-1, 0), (1, 0), (0, -1), (0, 1)]:  # 四方向
            nx, ny = x + dx, y + dy
            if self.is_walkable(nx, ny):
                neighbors.append((nx, ny))
        return neighbors
    
    def manhattan_distance(self, pos1: Tuple[int, int], pos2: Tuple[int, int]) -> int:
        """计算曼哈顿距离"""
        return abs(pos1[0] - pos2[0]) + abs(pos1[1] - pos2[1])
    
    def get_nearest_unload_zone(self, x: int, y: int) -> Tuple[int, int]:
        """获取最近的卸货区域"""
        min_dist = float('inf')
        nearest_zone = self.unload_zones[0]
        
        for zone in self.unload_zones:
            dist = self.manhattan_distance((x, y), zone)
            if dist < min_dist:
                min_dist = dist
                nearest_zone = zone
        
        return nearest_zone
    
    def get_random_walkable_position(self) -> Tuple[int, int]:
        """获取随机可通行位置"""
        return np.random.choice(list(self.walkable_positions))
    
    def print_map(self):
        """打印地图（用于调试）"""
        print("地图布局 (0=可通行, 1=货架, 2=卸货区域):")
        for y in range(self.height):
            row = ""
            for x in range(self.width):
                row += str(self.grid[y, x]) + " "
            print(row)
        print()
        
        print("货架位置:")
        for i, (x, y) in enumerate(self.shelf_positions):
            print(f"货架{i+1}: ({x}, {y})")
        print()
        
        print("卸货区域:")
        for i, (x, y) in enumerate(self.unload_zones):
            print(f"卸货区域{i+1}: ({x}, {y})")


if __name__ == "__main__":
    # 测试地图管理器
    map_manager = MapManager()
    map_manager.print_map()
    
    # 测试一些功能
    print(f"位置(5, 5)可通行: {map_manager.is_walkable(5, 5)}")
    print(f"位置(0, 0)是卸货区域: {map_manager.is_unload_zone(0, 0)}")
    print(f"总可通行位置数: {len(map_manager.get_walkable_positions())}")
