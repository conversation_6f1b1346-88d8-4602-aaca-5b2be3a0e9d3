"""
任务管理器 - 多载重AGV通信增强调度系统
负责管理16个运输任务，包括任务生成、状态跟踪和分配管理
"""

import numpy as np
from typing import List, Tuple, Dict, Optional, Set
from enum import Enum
from utils.config import ENV_CONFIG, STATE_CONFIG
from envs.map_manager import MapManager


class TaskStatus(Enum):
    """任务状态枚举"""
    AVAILABLE = 0    # 未分配
    ASSIGNED = 1     # 已分配
    COMPLETED = 2    # 已完成


class Task:
    """任务类"""
    
    def __init__(self, task_id: int, position: Tuple[int, int], weight: int):
        """初始化任务
        
        Args:
            task_id: 任务ID
            position: 任务位置（货架位置）
            weight: 任务重量（5或10）
        """
        self.id = task_id
        self.position = position
        self.weight = weight
        self.status = TaskStatus.AVAILABLE
        self.assigned_agv = None  # 分配给哪个AGV
        self.pickup_time = None   # 拾取时间
        self.completion_time = None  # 完成时间
    
    def assign_to_agv(self, agv_id: int, current_time: int):
        """分配任务给AGV"""
        self.status = TaskStatus.ASSIGNED
        self.assigned_agv = agv_id
        self.pickup_time = current_time
    
    def complete_task(self, current_time: int):
        """完成任务"""
        self.status = TaskStatus.COMPLETED
        self.completion_time = current_time
    
    def reset(self):
        """重置任务状态"""
        self.status = TaskStatus.AVAILABLE
        self.assigned_agv = None
        self.pickup_time = None
        self.completion_time = None
    
    def get_state_vector(self, map_width: int, map_height: int) -> np.ndarray:
        """获取任务状态向量（4维）
        
        Returns:
            [x_norm, y_norm, weight_norm, status]
        """
        x_norm = self.position[0] / (map_width - 1)
        y_norm = self.position[1] / (map_height - 1)
        
        # 重量归一化：5->0.5, 10->1.0
        weight_norm = 0.5 if self.weight == 5 else 1.0
        
        # 状态值
        status_value = self.status.value
        
        return np.array([x_norm, y_norm, weight_norm, status_value], dtype=np.float32)


class TaskManager:
    """任务管理器类"""
    
    def __init__(self, map_manager: MapManager):
        """初始化任务管理器
        
        Args:
            map_manager: 地图管理器实例
        """
        self.map_manager = map_manager
        self.num_tasks = ENV_CONFIG.NUM_TASKS
        self.task_weights = ENV_CONFIG.TASK_WEIGHTS.copy()
        
        # 任务列表
        self.tasks: List[Task] = []
        
        # 统计信息
        self.total_completed = 0
        self.total_assigned = 0
        
        # 初始化任务
        self._initialize_tasks()
    
    def _initialize_tasks(self):
        """初始化所有任务"""
        shelf_positions = self.map_manager.get_shelf_positions()

        # 确保每个货架最多只有一个任务
        if self.num_tasks > len(shelf_positions):
            print(f"警告: 任务数量({self.num_tasks})超过货架数量({len(shelf_positions)})")
            print(f"将任务数量调整为货架数量: {len(shelf_positions)}")
            self.num_tasks = len(shelf_positions)
            # 调整任务重量列表
            self.task_weights = self.task_weights[:self.num_tasks]

        # 随机选择货架位置分布任务（不允许重复）
        selected_positions = np.random.choice(
            len(shelf_positions),
            size=self.num_tasks,
            replace=False  # 不允许重复选择同一个货架
        )

        # 随机打乱任务重量
        np.random.shuffle(self.task_weights)

        # 创建任务
        for i in range(self.num_tasks):
            position = shelf_positions[selected_positions[i]]
            weight = self.task_weights[i]
            task = Task(task_id=i, position=position, weight=weight)
            self.tasks.append(task)

        print(f"初始化{self.num_tasks}个任务:")
        for task in self.tasks:
            print(f"  任务{task.id}: 位置{task.position}, 重量{task.weight}")

    def _reinitialize_existing_tasks(self):
        """重新初始化现有任务的位置和重量（不创建新任务）"""
        shelf_positions = self.map_manager.get_shelf_positions()

        # 确保每个货架最多只有一个任务
        if self.num_tasks > len(shelf_positions):
            print(f"警告: 任务数量({self.num_tasks})超过货架数量({len(shelf_positions)})")
            # 在重新初始化时，保持现有任务数量不变，但确保不重复
            actual_num_tasks = min(self.num_tasks, len(shelf_positions))
        else:
            actual_num_tasks = self.num_tasks

        # 随机选择货架位置分布任务（不允许重复）
        selected_positions = np.random.choice(
            len(shelf_positions),
            size=actual_num_tasks,
            replace=False  # 不允许重复选择同一个货架
        )

        # 随机打乱任务重量
        np.random.shuffle(self.task_weights)

        # 重新分配现有任务的位置和重量
        for i, task in enumerate(self.tasks):
            if i < actual_num_tasks:
                task.position = shelf_positions[selected_positions[i]]
                task.weight = self.task_weights[i]
            else:
                # 如果任务数量超过货架数量，将多余任务设为不可用
                task.status = TaskStatus.COMPLETED  # 或者设为其他不可用状态

        # print(f"重新初始化{self.num_tasks}个任务:")  # 减少冗余输出
        # for task in self.tasks:
        #     print(f"  任务{task.id}: 位置{task.position}, 重量{task.weight}")  # 减少冗余输出

    def reset(self):
        """重置所有任务状态"""
        for task in self.tasks:
            task.reset()

        self.total_completed = 0
        self.total_assigned = 0

        # 重新随机分布任务位置和重量（不创建新任务，只重新分配现有任务）
        self._reinitialize_existing_tasks()
    
    def get_available_tasks(self) -> List[Task]:
        """获取所有可用任务"""
        return [task for task in self.tasks if task.status == TaskStatus.AVAILABLE]
    
    def get_assigned_tasks(self) -> List[Task]:
        """获取所有已分配任务"""
        return [task for task in self.tasks if task.status == TaskStatus.ASSIGNED]
    
    def get_completed_tasks(self) -> List[Task]:
        """获取所有已完成任务"""
        return [task for task in self.tasks if task.status == TaskStatus.COMPLETED]
    
    def get_task_by_id(self, task_id: int) -> Optional[Task]:
        """根据ID获取任务"""
        if 0 <= task_id < len(self.tasks):
            return self.tasks[task_id]
        return None
    
    def get_tasks_by_agv(self, agv_id: int) -> List[Task]:
        """获取分配给指定AGV的任务"""
        return [task for task in self.tasks if task.assigned_agv == agv_id]
    
    def assign_task(self, task_id: int, agv_id: int, current_time: int) -> bool:
        """分配任务给AGV
        
        Args:
            task_id: 任务ID
            agv_id: AGV ID
            current_time: 当前时间步
            
        Returns:
            是否分配成功
        """
        task = self.get_task_by_id(task_id)
        if task and task.status == TaskStatus.AVAILABLE:
            task.assign_to_agv(agv_id, current_time)
            self.total_assigned += 1
            return True
        return False
    
    def complete_task(self, task_id: int, current_time: int) -> bool:
        """完成任务
        
        Args:
            task_id: 任务ID
            current_time: 当前时间步
            
        Returns:
            是否完成成功
        """
        task = self.get_task_by_id(task_id)
        if task and task.status == TaskStatus.ASSIGNED:
            task.complete_task(current_time)
            self.total_completed += 1
            return True
        return False
    
    def complete_tasks_by_agv(self, agv_id: int, current_time: int) -> List[int]:
        """完成指定AGV的所有任务（批量卸货）
        
        Args:
            agv_id: AGV ID
            current_time: 当前时间步
            
        Returns:
            完成的任务ID列表
        """
        completed_task_ids = []
        agv_tasks = self.get_tasks_by_agv(agv_id)
        
        for task in agv_tasks:
            if task.status == TaskStatus.ASSIGNED:
                task.complete_task(current_time)
                self.total_completed += 1
                completed_task_ids.append(task.id)
        
        return completed_task_ids
    
    def get_tasks_at_position(self, position: Tuple[int, int]) -> List[Task]:
        """获取指定位置的可用任务"""
        return [task for task in self.get_available_tasks() 
                if task.position == position]
    
    def get_task_weight_at_position(self, position: Tuple[int, int]) -> int:
        """获取指定位置的任务总重量"""
        tasks = self.get_tasks_at_position(position)
        return sum(task.weight for task in tasks)
    
    def can_pickup_tasks(self, position: Tuple[int, int], current_load: int, max_capacity: int) -> List[Task]:
        """检查在指定位置可以拾取的任务
        
        Args:
            position: 位置
            current_load: 当前载重
            max_capacity: 最大载重
            
        Returns:
            可以拾取的任务列表
        """
        available_tasks = self.get_tasks_at_position(position)
        pickable_tasks = []
        
        remaining_capacity = max_capacity - current_load
        
        # 按重量排序，优先选择重量小的任务
        available_tasks.sort(key=lambda t: t.weight)
        
        for task in available_tasks:
            if task.weight <= remaining_capacity:
                pickable_tasks.append(task)
                remaining_capacity -= task.weight
        
        return pickable_tasks
    
    def get_all_task_states(self) -> np.ndarray:
        """获取所有任务的状态向量
        
        Returns:
            形状为(num_tasks, 4)的状态矩阵
        """
        states = []
        for task in self.tasks:
            state = task.get_state_vector(
                self.map_manager.width, 
                self.map_manager.height
            )
            states.append(state)
        
        return np.array(states, dtype=np.float32)
    
    def get_completion_rate(self) -> float:
        """获取任务完成率"""
        return self.total_completed / self.num_tasks if self.num_tasks > 0 else 0.0
    
    def get_assignment_rate(self) -> float:
        """获取任务分配率"""
        return len(self.get_assigned_tasks()) / self.num_tasks if self.num_tasks > 0 else 0.0
    
    def get_statistics(self) -> Dict:
        """获取任务统计信息"""
        return {
            'total_tasks': self.num_tasks,
            'available_tasks': len(self.get_available_tasks()),
            'assigned_tasks': len(self.get_assigned_tasks()),
            'completed_tasks': len(self.get_completed_tasks()),
            'completion_rate': self.get_completion_rate(),
            'assignment_rate': self.get_assignment_rate(),
            'total_weight': sum(task.weight for task in self.tasks),
            'completed_weight': sum(task.weight for task in self.get_completed_tasks())
        }
    
    def print_status(self):
        """打印任务状态（用于调试）"""
        stats = self.get_statistics()
        print("=== 任务状态 ===")
        print(f"总任务数: {stats['total_tasks']}")
        print(f"可用任务: {stats['available_tasks']}")
        print(f"已分配任务: {stats['assigned_tasks']}")
        print(f"已完成任务: {stats['completed_tasks']}")
        print(f"完成率: {stats['completion_rate']:.2%}")
        print(f"总重量: {stats['total_weight']}")
        print(f"已完成重量: {stats['completed_weight']}")
        print()


if __name__ == "__main__":
    # 测试任务管理器
    from envs.map_manager import MapManager
    
    map_manager = MapManager()
    task_manager = TaskManager(map_manager)
    
    task_manager.print_status()
    
    # 测试任务分配
    print("测试任务分配:")
    available_tasks = task_manager.get_available_tasks()
    if available_tasks:
        task = available_tasks[0]
        success = task_manager.assign_task(task.id, agv_id=0, current_time=10)
        print(f"分配任务{task.id}给AGV 0: {success}")
        
        # 测试任务完成
        success = task_manager.complete_task(task.id, current_time=50)
        print(f"完成任务{task.id}: {success}")
    
    task_manager.print_status()
