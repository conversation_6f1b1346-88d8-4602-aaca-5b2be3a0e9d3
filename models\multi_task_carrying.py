"""
多任务携带管理器 - 基于单层AGV-Task-AGV注意力机制的MAPPO多AGV调度优化系统
实现AGV多任务同时携带的决策和管理机制
"""

import numpy as np
from typing import List, Dict, Optional, Tuple, Set
from dataclasses import dataclass
from utils.config import ENV_CONFIG, ACTION_CONFIG


@dataclass
class CarryingPlan:
    """携带计划"""
    task_sequence: List[int]  # 任务拾取顺序
    pickup_path: List[Tuple[int, int]]  # 拾取路径
    unload_position: Tuple[int, int]  # 卸货位置
    total_weight: int  # 总重量
    estimated_time: int  # 预估时间
    efficiency_score: float  # 效率评分


@dataclass
class CarryingState:
    """携带状态"""
    agv_id: int
    current_tasks: List[int]  # 当前携带任务
    remaining_capacity: int  # 剩余载重
    carrying_efficiency: float  # 携带效率
    next_recommended_tasks: List[int]  # 推荐的下一批任务


class MultiTaskCarryingManager:
    """多任务携带管理器
    
    实现研究方案中的多任务携带机制：
    1. 支持AGV同时携带多个任务的决策机制
    2. 动态任务组合优化，在执行过程中调整任务选择
    3. 多任务协同奖励，鼓励高效的多任务组合
    """
    
    def __init__(self):
        """初始化多任务携带管理器"""
        self.carrying_states = {}  # AGV ID -> CarryingState
        self.carrying_history = {}  # AGV ID -> 历史记录
        self.efficiency_metrics = {}
        
    def update_agv_carrying_state(self, agv, available_tasks: List, 
                                current_time: int) -> Dict[str, any]:
        """更新AGV携带状态
        
        Args:
            agv: AGV对象
            available_tasks: 可用任务列表
            current_time: 当前时间步
            
        Returns:
            更新后的携带状态信息
        """
        agv_id = agv.id
        
        # 计算当前携带效率
        carrying_efficiency = self._calculate_carrying_efficiency(agv)
        
        # 推荐下一批任务
        recommended_tasks = self._recommend_next_tasks(agv, available_tasks)
        
        # 更新携带状态
        carrying_state = CarryingState(
            agv_id=agv_id,
            current_tasks=agv.carrying_tasks.copy(),
            remaining_capacity=agv.get_remaining_capacity(),
            carrying_efficiency=carrying_efficiency,
            next_recommended_tasks=recommended_tasks
        )
        
        self.carrying_states[agv_id] = carrying_state
        
        # 更新历史记录
        self._update_carrying_history(agv_id, carrying_state, current_time)
        
        return {
            'carrying_state': carrying_state,
            'efficiency_trend': self._get_efficiency_trend(agv_id),
            'optimization_suggestions': self._generate_carrying_suggestions(agv, available_tasks)
        }
    
    def generate_carrying_plan(self, agv, target_tasks: List, 
                             unload_zones: List[Tuple[int, int]]) -> Optional[CarryingPlan]:
        """生成携带计划
        
        Args:
            agv: AGV对象
            target_tasks: 目标任务列表
            unload_zones: 卸货区域列表
            
        Returns:
            携带计划（如果可行）
        """
        if not target_tasks:
            return None
        
        # 检查载重约束
        total_weight = sum(task.weight for task in target_tasks)
        if total_weight > agv.get_remaining_capacity():
            return None
        
        # 优化任务拾取顺序
        optimized_sequence = self._optimize_pickup_sequence(agv, target_tasks)
        
        # 计算拾取路径
        pickup_path = self._calculate_pickup_path(agv, optimized_sequence)
        
        # 选择最优卸货位置
        optimal_unload = self._select_optimal_unload_position(
            optimized_sequence[-1].position if optimized_sequence else agv.position,
            unload_zones
        )
        
        # 估算执行时间
        estimated_time = self._estimate_execution_time(pickup_path, optimal_unload)
        
        # 计算效率评分
        efficiency_score = self._calculate_plan_efficiency(
            total_weight, len(pickup_path), estimated_time
        )
        
        return CarryingPlan(
            task_sequence=[task.id for task in optimized_sequence],
            pickup_path=pickup_path,
            unload_position=optimal_unload,
            total_weight=total_weight,
            estimated_time=estimated_time,
            efficiency_score=efficiency_score
        )
    
    def add_task_to_agv(self, agv_id: int, task, current_time: int) -> bool:
        """为AGV添加携带任务
        
        Args:
            agv_id: AGV ID
            task: 任务对象
            current_time: 当前时间
            
        Returns:
            是否成功添加
        """
        if agv_id not in self.carrying_states:
            return False
        
        carrying_state = self.carrying_states[agv_id]
        
        # 检查载重约束
        if carrying_state.remaining_capacity < task.weight:
            return False
        
        # 更新携带状态
        carrying_state.current_tasks.append(task.id)
        carrying_state.remaining_capacity -= task.weight
        carrying_state.carrying_efficiency = self._recalculate_efficiency(carrying_state)
        
        # 记录添加事件
        self._record_task_addition(agv_id, task.id, current_time)
        
        return True
    
    def _calculate_carrying_efficiency(self, agv) -> float:
        """计算携带效率"""
        if not agv.carrying_tasks:
            return 0.0
        
        # 载重利用率
        load_utilization = agv.current_load / agv.max_capacity
        
        # 任务数量奖励（鼓励多任务携带）
        task_count_bonus = min(1.0, len(agv.carrying_tasks) / 3.0)  # 最多3个任务
        
        # 综合效率
        efficiency = 0.7 * load_utilization + 0.3 * task_count_bonus
        
        return efficiency
    
    def _recommend_next_tasks(self, agv, available_tasks: List) -> List[int]:
        """推荐下一批任务"""
        if not available_tasks:
            return []
        
        recommendations = []
        remaining_capacity = agv.get_remaining_capacity()
        
        # 过滤可用任务
        valid_tasks = [task for task in available_tasks 
                      if (hasattr(task, 'status') and task.status.name == 'AVAILABLE' 
                          and task.weight <= remaining_capacity)]
        
        if not valid_tasks:
            return []
        
        # 按效益评分排序
        scored_tasks = []
        for task in valid_tasks:
            score = self._calculate_task_recommendation_score(agv, task)
            scored_tasks.append((task.id, score))
        
        scored_tasks.sort(key=lambda x: x[1], reverse=True)
        
        # 选择前3个推荐任务
        recommendations = [task_id for task_id, _ in scored_tasks[:3]]
        
        return recommendations
    
    def _calculate_task_recommendation_score(self, agv, task) -> float:
        """计算任务推荐评分"""
        # 距离因子（距离越近评分越高）
        distance = abs(agv.position[0] - task.position[0]) + abs(agv.position[1] - task.position[1])
        distance_score = max(0, 1.0 - distance / 20.0)  # 假设最大合理距离为20
        
        # 重量因子（重量适中评分较高）
        weight_score = task.weight / 15.0  # 归一化到0-1
        
        # 载重匹配因子（能更好利用剩余载重的任务评分更高）
        remaining_capacity = agv.get_remaining_capacity()
        capacity_match_score = task.weight / remaining_capacity if remaining_capacity > 0 else 0
        
        # 综合评分
        total_score = (
            0.4 * distance_score +
            0.3 * weight_score +
            0.3 * capacity_match_score
        )
        
        return total_score
    
    def _optimize_pickup_sequence(self, agv, tasks: List) -> List:
        """优化任务拾取顺序"""
        if len(tasks) <= 1:
            return tasks
        
        # 使用贪心算法优化拾取顺序
        current_pos = agv.position
        remaining_tasks = tasks.copy()
        optimized_sequence = []
        
        while remaining_tasks:
            # 找到距离当前位置最近的任务
            best_task = None
            best_distance = float('inf')
            
            for task in remaining_tasks:
                distance = abs(current_pos[0] - task.position[0]) + abs(current_pos[1] - task.position[1])
                if distance < best_distance:
                    best_distance = distance
                    best_task = task
            
            if best_task:
                optimized_sequence.append(best_task)
                remaining_tasks.remove(best_task)
                current_pos = best_task.position
        
        return optimized_sequence
    
    def _calculate_pickup_path(self, agv, task_sequence: List) -> List[Tuple[int, int]]:
        """计算拾取路径"""
        if not task_sequence:
            return [agv.position]
        
        path = [agv.position]
        
        for task in task_sequence:
            path.append(task.position)
        
        return path
    
    def _select_optimal_unload_position(self, last_pickup_pos: Tuple[int, int],
                                      unload_zones: List[Tuple[int, int]]) -> Tuple[int, int]:
        """选择最优卸货位置"""
        if not unload_zones:
            return (0, 0)  # 默认位置
        
        # 选择距离最后拾取位置最近的卸货区
        best_unload = unload_zones[0]
        best_distance = float('inf')
        
        for unload_pos in unload_zones:
            distance = abs(last_pickup_pos[0] - unload_pos[0]) + abs(last_pickup_pos[1] - unload_pos[1])
            if distance < best_distance:
                best_distance = distance
                best_unload = unload_pos
        
        return best_unload
    
    def _estimate_execution_time(self, pickup_path: List[Tuple[int, int]], 
                               unload_position: Tuple[int, int]) -> int:
        """估算执行时间"""
        if not pickup_path:
            return 0
        
        # 计算总路径长度
        total_distance = 0
        
        for i in range(1, len(pickup_path)):
            prev_pos = pickup_path[i-1]
            curr_pos = pickup_path[i]
            total_distance += abs(prev_pos[0] - curr_pos[0]) + abs(prev_pos[1] - curr_pos[1])
        
        # 加上到卸货区的距离
        last_pos = pickup_path[-1]
        total_distance += abs(last_pos[0] - unload_position[0]) + abs(last_pos[1] - unload_position[1])
        
        # 假设每步需要1个时间单位，加上装载和卸载时间
        loading_time = len(pickup_path) - 1  # 每个任务装载需要1个时间单位
        unloading_time = 1  # 卸载需要1个时间单位
        
        return total_distance + loading_time + unloading_time
    
    def _calculate_plan_efficiency(self, total_weight: int, path_length: int, 
                                 estimated_time: int) -> float:
        """计算计划效率评分"""
        if estimated_time == 0:
            return 0.0
        
        # 载重效率
        load_efficiency = total_weight / ENV_CONFIG.MAX_CAPACITY
        
        # 时间效率
        max_reasonable_time = ENV_CONFIG.MAP_WIDTH + ENV_CONFIG.MAP_HEIGHT + 10
        time_efficiency = max(0, 1.0 - estimated_time / max_reasonable_time)
        
        # 综合效率
        efficiency = 0.6 * load_efficiency + 0.4 * time_efficiency
        
        return efficiency
    
    def _update_carrying_history(self, agv_id: int, carrying_state: CarryingState, 
                               current_time: int):
        """更新携带历史记录"""
        if agv_id not in self.carrying_history:
            self.carrying_history[agv_id] = []
        
        record = {
            'time': current_time,
            'task_count': len(carrying_state.current_tasks),
            'remaining_capacity': carrying_state.remaining_capacity,
            'efficiency': carrying_state.carrying_efficiency
        }
        
        self.carrying_history[agv_id].append(record)
        
        # 保持历史记录在合理范围内
        if len(self.carrying_history[agv_id]) > 100:
            self.carrying_history[agv_id] = self.carrying_history[agv_id][-100:]
    
    def _get_efficiency_trend(self, agv_id: int) -> Dict[str, float]:
        """获取效率趋势"""
        if agv_id not in self.carrying_history or len(self.carrying_history[agv_id]) < 2:
            return {'trend': 0.0, 'stability': 0.0}
        
        history = self.carrying_history[agv_id]
        recent_efficiencies = [record['efficiency'] for record in history[-10:]]
        
        # 计算趋势（最近效率 - 历史平均效率）
        if len(recent_efficiencies) > 1:
            recent_avg = np.mean(recent_efficiencies[-5:])
            historical_avg = np.mean(recent_efficiencies[:-5]) if len(recent_efficiencies) > 5 else recent_avg
            trend = recent_avg - historical_avg
        else:
            trend = 0.0
        
        # 计算稳定性
        stability = 1.0 - np.std(recent_efficiencies) if len(recent_efficiencies) > 1 else 1.0
        
        return {'trend': trend, 'stability': max(0, stability)}
    
    def _generate_carrying_suggestions(self, agv, available_tasks: List) -> List[str]:
        """生成携带建议"""
        suggestions = []
        
        current_efficiency = self._calculate_carrying_efficiency(agv)
        remaining_capacity = agv.get_remaining_capacity()
        
        if current_efficiency < 0.5:
            suggestions.append("当前携带效率较低，建议增加任务数量")
        
        if remaining_capacity > 10:
            suggestions.append(f"剩余载重{remaining_capacity}单位，建议添加更多任务")
        
        if len(agv.carrying_tasks) == 1 and remaining_capacity >= 5:
            suggestions.append("当前只携带一个任务，建议考虑多任务组合")
        
        # 检查是否有合适的任务可以添加
        suitable_tasks = [task for task in available_tasks 
                         if (hasattr(task, 'status') and task.status.name == 'AVAILABLE' 
                             and task.weight <= remaining_capacity)]
        
        if suitable_tasks:
            suggestions.append(f"发现{len(suitable_tasks)}个可添加的任务")
        
        return suggestions
    
    def get_carrying_statistics(self, agv_ids: List[int]) -> Dict[str, float]:
        """获取多任务携带统计信息"""
        if not agv_ids:
            return {'average_efficiency': 0.0, 'multi_task_rate': 0.0}
        
        total_efficiency = 0.0
        multi_task_count = 0
        total_records = 0
        
        for agv_id in agv_ids:
            if agv_id in self.carrying_history:
                history = self.carrying_history[agv_id]
                for record in history[-20:]:  # 最近20条记录
                    total_efficiency += record['efficiency']
                    if record['task_count'] > 1:
                        multi_task_count += 1
                    total_records += 1
        
        if total_records == 0:
            return {'average_efficiency': 0.0, 'multi_task_rate': 0.0}
        
        return {
            'average_efficiency': total_efficiency / total_records,
            'multi_task_rate': multi_task_count / total_records,
            'total_carrying_events': total_records
        }
    
    def _record_task_addition(self, agv_id: int, task_id: int, current_time: int):
        """记录任务添加事件"""
        if agv_id not in self.efficiency_metrics:
            self.efficiency_metrics[agv_id] = []
        
        self.efficiency_metrics[agv_id].append({
            'event': 'task_added',
            'task_id': task_id,
            'time': current_time
        })
    
    def _recalculate_efficiency(self, carrying_state: CarryingState) -> float:
        """重新计算携带效率"""
        if not carrying_state.current_tasks:
            return 0.0
        
        # 基于当前状态重新计算效率
        load_utilization = (ENV_CONFIG.MAX_CAPACITY - carrying_state.remaining_capacity) / ENV_CONFIG.MAX_CAPACITY
        task_count_bonus = min(1.0, len(carrying_state.current_tasks) / 3.0)
        
        return 0.7 * load_utilization + 0.3 * task_count_bonus
