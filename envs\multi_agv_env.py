"""
多AGV环境 - 基于单层AGV-Task-AGV注意力机制的MAPPO多AGV调度优化系统
基于Ray RLlib的MultiAgentEnv，实现26×10网格世界的多AGV协同调度
"""

import numpy as np
import gymnasium as gym
from gymnasium import spaces
from ray.rllib.env.multi_agent_env import MultiAgentEnv
from typing import Dict, List, Tuple, Any, Optional, Set

from utils.config import ENV_CONFIG, STATE_CONFIG, ACTION_CONFIG, REWARD_CONFIG
from envs.map_manager import MapManager
from envs.task_manager import TaskManager, TaskStatus
from envs.agv_manager import AGVManager, AGVStatus
from models.autonomous_task_selection import AutonomousTaskSelector
from models.load_optimization import LoadUtilizationMaximizer
from models.path_load_balance import PathLoadBalanceManager
from models.multi_task_carrying import MultiTaskCarryingManager
from models.multi_objective_reward import MultiObjectiveRewardSystem, RewardMetrics


class MultiAGVEnv(MultiAgentEnv):
    """多AGV环境类"""
    
    def __init__(self, config: Dict = None):
        """初始化环境
        
        Args:
            config: 环境配置字典
        """
        super().__init__()
        
        # 配置参数
        self.config = config or {}
        self.num_agvs = self.config.get('num_agvs', ENV_CONFIG.NUM_AGVS)
        self.num_tasks = self.config.get('num_tasks', ENV_CONFIG.NUM_TASKS)
        self.max_episode_steps = self.config.get('max_episode_steps', ENV_CONFIG.MAX_EPISODE_STEPS)
        
        # 初始化管理器
        self.map_manager = MapManager()
        self.task_manager = TaskManager(self.map_manager)
        self.agv_manager = AGVManager(self.map_manager)
        
        # 环境状态
        self.current_step = 0
        self.episode_rewards = {f"agv_{i}": 0.0 for i in range(self.num_agvs)}
        self.collision_count = 0

        # 自主任务选择器
        self.autonomous_selector = AutonomousTaskSelector()

        # 载重优化器
        self.load_optimizer = LoadUtilizationMaximizer()

        # 路径-载重平衡管理器
        self.path_load_balancer = PathLoadBalanceManager()

        # 多任务携带管理器
        self.multi_task_manager = MultiTaskCarryingManager()

        # 多目标奖励系统
        reward_config = self.config.get('reward_config', {})
        self.reward_system = MultiObjectiveRewardSystem(reward_config)

        # 定义智能体ID
        self.agent_ids = [f"agv_{i}" for i in range(self.num_agvs)]
        
        # 定义观测空间和动作空间
        self._setup_spaces()
        
        print(f"多AGV环境初始化完成: {self.num_agvs}个AGV, {self.num_tasks}个任务")
    
    def _setup_spaces(self):
        """设置观测空间和动作空间"""
        # 观测空间：AGV状态(5) + 所有任务状态(16*4) + 全局状态(8) = 77维
        obs_dim = (STATE_CONFIG.AGV_STATE_DIM + 
                  self.num_tasks * STATE_CONFIG.TASK_STATE_DIM + 
                  STATE_CONFIG.GLOBAL_STATE_DIM)
        
        self.observation_space = spaces.Box(
            low=-1.0, high=1.0, shape=(obs_dim,), dtype=np.float32
        )
        
        # 动作空间：任务选择(18) + 运动控制(5) = 23维
        # 使用MultiDiscrete空间表示层次化动作
        self.action_space = spaces.MultiDiscrete([
            ACTION_CONFIG.TASK_ACTION_DIM,    # 任务选择动作
            ACTION_CONFIG.MOTION_ACTION_DIM   # 运动控制动作
        ])
        
        print(f"观测空间维度: {obs_dim}, 动作空间: {self.action_space}")
    
    def reset(self, *, seed: Optional[int] = None, options: Optional[Dict] = None) -> Tuple[Dict, Dict]:
        """重置环境
        
        Returns:
            observations: 各智能体的观测
            infos: 额外信息
        """
        if seed is not None:
            np.random.seed(seed)
        
        # 重置所有管理器
        self.task_manager.reset()
        self.agv_manager.reset()
        
        # 重置环境状态
        self.current_step = 0
        self.episode_rewards = {f"agv_{i}": 0.0 for i in range(self.num_agvs)}
        self.collision_count = 0

        # 更新奖励系统的episode信息
        episode_count = getattr(self, 'total_episodes', 0) + 1
        self.total_episodes = episode_count
        self.reward_system.update_episode(episode_count)

        # 获取初始观测
        observations = self._get_observations()
        infos = self._get_infos()
        
        return observations, infos
    
    def step(self, action_dict: Dict[str, Any]) -> Tuple[Dict, Dict, Dict, Dict, Dict]:
        """执行一步
        
        Args:
            action_dict: 各智能体的动作字典
            
        Returns:
            observations: 新观测
            rewards: 奖励
            terminateds: 是否终止
            truncateds: 是否截断
            infos: 额外信息
        """
        # 执行动作
        self._execute_actions(action_dict)
        
        # 更新环境状态
        self.current_step += 1
        self.agv_manager.update_all_statistics()
        
        # 计算奖励
        rewards = self._calculate_rewards()
        
        # 检查终止条件
        terminateds = self._check_terminateds()
        truncateds = self._check_truncateds()
        
        # 获取新观测和信息
        observations = self._get_observations()
        infos = self._get_infos()
        
        # 更新累积奖励
        for agent_id, reward in rewards.items():
            self.episode_rewards[agent_id] += reward
        
        return observations, rewards, terminateds, truncateds, infos
    
    def _execute_actions(self, action_dict: Dict[str, Any]):
        """执行所有智能体的动作"""
        for agent_id, actions in action_dict.items():
            agv_id = int(agent_id.split('_')[1])
            task_action, motion_action = actions
            
            # 执行任务选择动作
            self._execute_task_action(agv_id, task_action)
            
            # 执行运动控制动作
            self._execute_motion_action(agv_id, motion_action)
    
    def _execute_task_action(self, agv_id: int, task_action: int):
        """执行任务选择动作"""
        agv = self.agv_manager.get_agv(agv_id)
        if not agv:
            return
        
        if task_action == ACTION_CONFIG.KEEP_CURRENT:
            # 保持当前任务组合
            pass
        elif ACTION_CONFIG.SELECT_TASK_START <= task_action <= ACTION_CONFIG.SELECT_TASK_END:
            # 选择新任务
            task_id = task_action - ACTION_CONFIG.SELECT_TASK_START
            task = self.task_manager.get_task_by_id(task_id)
            
            if task and task.status == TaskStatus.AVAILABLE:
                # 检查载重约束
                if agv.get_remaining_capacity() >= task.weight:
                    # 放宽位置要求：在任务位置附近即可装载
                    distance = abs(agv.position[0] - task.position[0]) + abs(agv.position[1] - task.position[1])
                    if distance <= 1:  # 曼哈顿距离1以内即可装载
                        # 分配任务
                        if self.task_manager.assign_task(task_id, agv_id, self.current_step):
                            agv.add_task(task_id, task.weight)
                            agv.set_target(None, None)  # 清除目标
                            agv._just_loaded = True  # 标记刚刚装载
                            print(f"AGV {agv_id} 装载任务 {task_id} 在位置 {agv.position}, 任务位置 {task.position}")
                    else:
                        # 设置目标位置
                        agv.set_target(task_id, task.position)
        elif task_action == ACTION_CONFIG.GO_TO_UNLOAD:
            # 前往卸货区域
            if agv.carrying_tasks:
                nearest_unload = self.map_manager.get_nearest_unload_zone(agv.position[0], agv.position[1])
                agv.set_target(None, nearest_unload)
    
    def _execute_motion_action(self, agv_id: int, motion_action: int):
        """执行运动控制动作"""
        agv = self.agv_manager.get_agv(agv_id)
        if not agv:
            return
        
        if motion_action == ACTION_CONFIG.WAIT:
            # 等待动作
            agv.status = AGVStatus.IDLE
        else:
            # 移动动作
            success = self.agv_manager.move_agv(agv_id, motion_action)
            if not success:
                # 移动失败，可能是碰撞
                self.collision_count += 1
        
        # 检查是否到达卸货区域（放宽条件）
        unload_zones = self.map_manager.get_unload_zones()
        for unload_pos in unload_zones:
            distance = abs(agv.position[0] - unload_pos[0]) + abs(agv.position[1] - unload_pos[1])
            if distance <= 1:  # 在卸货区域附近即可卸货
                if agv.carrying_tasks:
                    # 执行卸货
                    completed_tasks = self.agv_manager.unload_agv_tasks(agv_id)
                    for task_id in completed_tasks:
                        self.task_manager.complete_task(task_id, self.current_step)
                    agv._just_unloaded = True  # 标记刚刚卸载
                    print(f"AGV {agv_id} 卸载 {len(completed_tasks)} 个任务在位置 {agv.position}")
                break
    
    def _get_observations(self) -> Dict[str, np.ndarray]:
        """获取所有智能体的观测"""
        observations = {}
        
        # 获取全局状态
        global_state = self._get_global_state()
        
        # 获取所有任务状态
        all_task_states = self.task_manager.get_all_task_states().flatten()
        
        for i in range(self.num_agvs):
            agent_id = f"agv_{i}"
            agv = self.agv_manager.get_agv(i)
            
            if agv:
                # AGV自身状态
                agv_state = agv.get_state_vector(
                    self.map_manager.width,
                    self.map_manager.height
                )
                
                # 组合观测：AGV状态 + 所有任务状态 + 全局状态
                observation = np.concatenate([
                    agv_state,
                    all_task_states,
                    global_state
                ], dtype=np.float32)
                
                observations[agent_id] = observation
            else:
                # 如果AGV不存在，返回零向量
                obs_dim = (STATE_CONFIG.AGV_STATE_DIM + 
                          self.num_tasks * STATE_CONFIG.TASK_STATE_DIM + 
                          STATE_CONFIG.GLOBAL_STATE_DIM)
                observations[agent_id] = np.zeros(obs_dim, dtype=np.float32)
        
        return observations
    
    def _get_global_state(self) -> np.ndarray:
        """获取环境全局状态（8维）"""
        # 已完成任务数归一化
        completed_norm = self.task_manager.get_completion_rate()
        
        # 活跃任务数归一化
        active_tasks = len(self.task_manager.get_assigned_tasks())
        active_norm = active_tasks / self.num_tasks
        
        # 平均载重利用率
        agv_stats = self.agv_manager.get_system_statistics()
        load_util = agv_stats['average_load_utilization']
        
        # 碰撞次数归一化
        max_collisions = self.num_agvs * self.max_episode_steps * 0.1  # 假设最大碰撞率10%
        collision_norm = min(self.collision_count / max_collisions, 1.0)
        
        # 时间步进度
        time_norm = self.current_step / self.max_episode_steps
        
        # 系统拥堵程度（简化计算）
        congestion = 0.0  # 需要更复杂的计算
        
        # 整体系统效率
        efficiency = completed_norm * load_util if load_util > 0 else 0.0
        
        # 路径最优性评分（简化）
        path_opt = 0.8  # 需要更复杂的计算
        
        return np.array([
            completed_norm, active_norm, load_util, collision_norm,
            time_norm, congestion, efficiency, path_opt
        ], dtype=np.float32)

    def _calculate_rewards(self) -> Dict[str, float]:
        """计算所有智能体的奖励"""
        rewards = {}

        # 构建环境状态
        environment_state = self._get_environment_state()

        for i in range(self.num_agvs):
            agent_id = f"agv_{i}"
            agv = self.agv_manager.get_agv(i)

            if agv:
                # 使用多目标奖励系统计算奖励
                total_reward, reward_metrics = self.reward_system.calculate_total_reward(
                    agv, environment_state
                )

                # 添加引导性奖励
                guidance_reward = self._calculate_guidance_reward(agv)
                total_reward += guidance_reward

                rewards[agent_id] = total_reward

                # 更新episode奖励统计
                self.episode_rewards[agent_id] += total_reward
            else:
                rewards[agent_id] = 0.0

        return rewards

    def _calculate_guidance_reward(self, agv) -> float:
        """计算引导性奖励，帮助AGV学会向任务移动和卸货"""
        reward = 0.0

        # 1. 向任务移动的奖励
        if not agv.carrying_tasks:  # 空载时，鼓励向最近任务移动
            available_tasks = self.task_manager.get_available_tasks()
            if available_tasks:
                # 找到最近的任务
                min_distance = float('inf')
                for task in available_tasks:
                    distance = abs(agv.position[0] - task.position[0]) + abs(agv.position[1] - task.position[1])
                    min_distance = min(min_distance, distance)

                # 距离越近，奖励越高
                if min_distance < float('inf'):
                    proximity_reward = max(0, 0.2 - min_distance * 0.01)  # 最大0.2奖励
                    reward += proximity_reward

        # 2. 向卸货区移动的奖励
        elif agv.carrying_tasks:  # 有载时，鼓励向卸货区移动
            unload_zones = self.map_manager.get_unload_zones()
            if unload_zones:
                # 找到最近的卸货区
                min_distance = float('inf')
                for unload_pos in unload_zones:
                    distance = abs(agv.position[0] - unload_pos[0]) + abs(agv.position[1] - unload_pos[1])
                    min_distance = min(min_distance, distance)

                # 距离越近，奖励越高
                if min_distance < float('inf'):
                    proximity_reward = max(0, 0.3 - min_distance * 0.015)  # 最大0.3奖励
                    reward += proximity_reward

        # 3. 装载任务的即时奖励
        if hasattr(agv, '_just_loaded') and agv._just_loaded:
            reward += 1.0  # 装载任务的即时奖励
            agv._just_loaded = False

        # 4. 卸载任务的即时奖励
        if hasattr(agv, '_just_unloaded') and agv._just_unloaded:
            reward += 2.0  # 卸载任务的即时奖励
            agv._just_unloaded = False

        return reward

    def _get_environment_state(self) -> Dict[str, Any]:
        """获取环境状态信息

        Returns:
            环境状态字典
        """
        return {
            'current_step': self.current_step,
            'max_episode_steps': self.max_episode_steps,
            'completion_rate': self.task_manager.get_completion_rate(),
            'collision_count': self.collision_count,
            'num_agvs': self.num_agvs,
            'num_tasks': self.num_tasks,
            'task_manager': self.task_manager,
            'agv_manager': self.agv_manager,
            'map_manager': self.map_manager
        }

    def _check_terminateds(self) -> Dict[str, bool]:
        """检查各智能体是否终止"""
        terminateds = {}

        # 检查是否所有任务都完成
        all_completed = self.task_manager.get_completion_rate() >= 1.0

        for agent_id in self.agent_ids:
            terminateds[agent_id] = all_completed

        # 添加__all__键
        terminateds["__all__"] = all_completed

        return terminateds

    def _check_truncateds(self) -> Dict[str, bool]:
        """检查各智能体是否截断"""
        truncateds = {}

        # 检查是否达到最大步数
        max_steps_reached = self.current_step >= self.max_episode_steps

        for agent_id in self.agent_ids:
            truncateds[agent_id] = max_steps_reached

        # 添加__all__键
        truncateds["__all__"] = max_steps_reached

        return truncateds

    def _get_infos(self) -> Dict[str, Dict]:
        """获取额外信息"""
        infos = {}

        # 全局统计信息
        global_info = {
            'step': self.current_step,
            'completion_rate': self.task_manager.get_completion_rate(),
            'collision_count': self.collision_count,
            'system_stats': self.agv_manager.get_system_statistics(),
            'task_stats': self.task_manager.get_statistics()
        }

        for i in range(self.num_agvs):
            agent_id = f"agv_{i}"
            agv = self.agv_manager.get_agv(i)

            if agv:
                agv_info = {
                    'position': agv.position,
                    'load': agv.current_load,
                    'load_utilization': agv.get_load_utilization(),
                    'carrying_tasks': agv.carrying_tasks.copy(),
                    'target_task': agv.target_task_id,
                    'target_position': agv.target_position,
                    'status': agv.status.name
                }
                agv_info.update(global_info)
                infos[agent_id] = agv_info
            else:
                infos[agent_id] = global_info.copy()

        return infos

    def get_action_mask(self, agent_id: str) -> Dict[str, np.ndarray]:
        """获取动作掩码（使用智能任务选择机制）"""
        agv_id = int(agent_id.split('_')[1])
        agv = self.agv_manager.get_agv(agv_id)

        if not agv:
            # 如果AGV不存在，所有动作都不可用
            return {
                'task_action_mask': np.zeros(ACTION_CONFIG.TASK_ACTION_DIM, dtype=bool),
                'motion_action_mask': np.zeros(ACTION_CONFIG.MOTION_ACTION_DIM, dtype=bool)
            }

        # 获取其他AGV列表
        other_agvs = [other_agv for other_agv in self.agv_manager.agvs if other_agv.id != agv_id]

        # 获取卸货区域
        unload_zones = self.map_manager.get_unload_zones()

        # 使用智能任务选择机制生成任务动作掩码
        task_mask = self.autonomous_selector.get_action_mask(
            agv=agv,
            all_tasks=self.task_manager.tasks,
            other_agvs=other_agvs,
            unload_zones=unload_zones
        )

        # 运动控制动作掩码（保持原有逻辑）
        motion_mask = np.ones(ACTION_CONFIG.MOTION_ACTION_DIM, dtype=bool)

        # 检查各个移动方向是否可行
        for action, (dx, dy) in ACTION_CONFIG.MOTION_VECTORS.items():
            new_pos = (agv.position[0] + dx, agv.position[1] + dy)

            # 检查是否可通行且不会碰撞
            if not self.map_manager.is_walkable(new_pos[0], new_pos[1]):
                motion_mask[action] = False
            elif self.agv_manager.check_collision(agv_id, new_pos):
                motion_mask[action] = False

        return {
            'task_action_mask': task_mask,
            'motion_action_mask': motion_mask
        }

    def get_optimal_task_combination(self, agent_id: str) -> Optional[Dict]:
        """获取最优任务组合建议

        Args:
            agent_id: 智能体ID

        Returns:
            最优任务组合信息（如果存在）
        """
        agv_id = int(agent_id.split('_')[1])
        agv = self.agv_manager.get_agv(agv_id)

        if not agv:
            return None

        # 获取其他AGV列表
        other_agvs = [other_agv for other_agv in self.agv_manager.agvs if other_agv.id != agv_id]

        # 获取卸货区域
        unload_zones = self.map_manager.get_unload_zones()

        # 获取最优任务组合
        optimal_combination = self.autonomous_selector.select_optimal_task_combination(
            agv=agv,
            all_tasks=self.task_manager.tasks,
            other_agvs=other_agvs,
            unload_zones=unload_zones
        )

        if optimal_combination:
            return {
                'task_ids': optimal_combination.task_ids,
                'total_weight': optimal_combination.total_weight,
                'positions': optimal_combination.positions,
                'load_utilization': optimal_combination.metrics.load_utilization,
                'path_efficiency': optimal_combination.metrics.path_efficiency,
                'collaboration_score': optimal_combination.metrics.collaboration_score,
                'total_score': optimal_combination.metrics.total_score
            }

        return None

    def get_task_selection_statistics(self) -> Dict[str, float]:
        """获取任务选择统计信息"""
        return self.autonomous_selector.get_selection_statistics()

    def get_load_optimization_result(self) -> Dict[str, any]:
        """获取载重优化结果

        Returns:
            载重优化结果字典
        """
        return self.load_optimizer.optimize_global_load_utilization(
            agvs=self.agv_manager.agvs,
            available_tasks=self.task_manager.tasks
        )

    def get_load_optimization_statistics(self) -> Dict[str, float]:
        """获取载重优化统计信息

        Returns:
            载重优化统计信息字典
        """
        return self.load_optimizer.get_optimization_statistics(
            agvs=self.agv_manager.agvs,
            available_tasks=self.task_manager.tasks
        )

    def get_system_load_metrics(self) -> Dict[str, float]:
        """获取系统载重指标

        Returns:
            系统载重指标字典
        """
        return self.load_optimizer.load_balancer.calculate_system_load_metrics(
            agvs=self.agv_manager.agvs
        )

    def detect_load_imbalance(self) -> Dict[str, any]:
        """检测载重不平衡

        Returns:
            载重不平衡检测结果
        """
        return self.load_optimizer.load_balancer.detect_load_imbalance(
            agvs=self.agv_manager.agvs
        )

    def get_path_load_balance_result(self) -> Dict[str, any]:
        """获取路径-载重平衡结果

        Returns:
            路径-载重平衡结果字典
        """
        unload_zones = self.map_manager.get_unload_zones()
        return self.path_load_balancer.optimize_path_load_balance(
            agvs=self.agv_manager.agvs,
            available_tasks=self.task_manager.tasks,
            unload_zones=unload_zones
        )

    def get_path_load_balance_statistics(self) -> Dict[str, float]:
        """获取路径-载重平衡统计信息

        Returns:
            路径-载重平衡统计信息字典
        """
        unload_zones = self.map_manager.get_unload_zones()
        return self.path_load_balancer.get_balance_statistics(
            agvs=self.agv_manager.agvs,
            available_tasks=self.task_manager.tasks,
            unload_zones=unload_zones
        )

    def get_multi_objective_candidates(self, agent_id: str) -> List[Dict]:
        """获取多目标优化候选方案

        Args:
            agent_id: 智能体ID

        Returns:
            多目标优化候选方案列表
        """
        agv_id = int(agent_id.split('_')[1])
        agv = self.agv_manager.get_agv(agv_id)

        if not agv:
            return []

        available_tasks = [task for task in self.task_manager.tasks
                          if task.status == TaskStatus.AVAILABLE]
        unload_zones = self.map_manager.get_unload_zones()

        candidates = self.path_load_balancer.multi_objective_optimizer.optimize_task_selection(
            agv=agv,
            available_tasks=available_tasks,
            unload_zones=unload_zones
        )

        # 转换为字典格式
        result = []
        for candidate in candidates:
            result.append({
                'task_ids': candidate.task_ids,
                'total_weight': candidate.total_weight,
                'path_length': candidate.path_length,
                'load_utilization': candidate.load_utilization,
                'path_efficiency': candidate.path_efficiency,
                'pareto_score': candidate.pareto_score,
                'balance_index': candidate.balance_index
            })

        return result

    def get_multi_task_carrying_state(self, agent_id: str) -> Dict[str, any]:
        """获取多任务携带状态

        Args:
            agent_id: 智能体ID

        Returns:
            多任务携带状态信息
        """
        agv_id = int(agent_id.split('_')[1])
        agv = self.agv_manager.get_agv(agv_id)

        if not agv:
            return {}

        return self.multi_task_manager.update_agv_carrying_state(
            agv=agv,
            available_tasks=self.task_manager.tasks,
            current_time=self.current_step
        )

    def generate_carrying_plan(self, agent_id: str, target_task_ids: List[int]) -> Optional[Dict]:
        """生成携带计划

        Args:
            agent_id: 智能体ID
            target_task_ids: 目标任务ID列表

        Returns:
            携带计划信息
        """
        agv_id = int(agent_id.split('_')[1])
        agv = self.agv_manager.get_agv(agv_id)

        if not agv:
            return None

        # 获取目标任务
        target_tasks = []
        for task_id in target_task_ids:
            task = self.task_manager.get_task_by_id(task_id)
            if task:
                target_tasks.append(task)

        if not target_tasks:
            return None

        unload_zones = self.map_manager.get_unload_zones()
        plan = self.multi_task_manager.generate_carrying_plan(agv, target_tasks, unload_zones)

        if plan:
            return {
                'task_sequence': plan.task_sequence,
                'pickup_path': plan.pickup_path,
                'unload_position': plan.unload_position,
                'total_weight': plan.total_weight,
                'estimated_time': plan.estimated_time,
                'efficiency_score': plan.efficiency_score
            }

        return None

    def get_multi_task_carrying_statistics(self) -> Dict[str, float]:
        """获取多任务携带统计信息

        Returns:
            多任务携带统计信息
        """
        agv_ids = [agv.id for agv in self.agv_manager.agvs]
        return self.multi_task_manager.get_carrying_statistics(agv_ids)

    def add_task_to_agv_carrying(self, agent_id: str, task_id: int) -> bool:
        """为AGV添加携带任务

        Args:
            agent_id: 智能体ID
            task_id: 任务ID

        Returns:
            是否成功添加
        """
        agv_id = int(agent_id.split('_')[1])
        task = self.task_manager.get_task_by_id(task_id)

        if not task:
            return False

        return self.multi_task_manager.add_task_to_agv(agv_id, task, self.current_step)

    def render(self, mode: str = 'human'):
        """渲染环境（简单文本输出）"""
        if mode == 'human':
            print(f"\n=== Step {self.current_step} ===")
            print("地图状态:")

            # 创建显示网格
            display_grid = np.full((self.map_manager.height, self.map_manager.width), '.', dtype=str)

            # 标记货架
            for y in range(self.map_manager.height):
                for x in range(self.map_manager.width):
                    if self.map_manager.is_shelf(x, y):
                        display_grid[y, x] = '#'
                    elif self.map_manager.is_unload_zone(x, y):
                        display_grid[y, x] = 'U'

            # 标记任务
            for task in self.task_manager.get_available_tasks():
                x, y = task.position
                display_grid[y, x] = str(task.weight)[0]  # 显示重量的第一位

            # 标记AGV
            for agv in self.agv_manager.agvs:
                x, y = agv.position
                display_grid[y, x] = str(agv.id)

            # 打印网格
            for row in display_grid:
                print(' '.join(row))

            # 打印统计信息
            print(f"完成率: {self.task_manager.get_completion_rate():.2%}")
            print(f"碰撞次数: {self.collision_count}")

            for agv in self.agv_manager.agvs:
                print(f"AGV{agv.id}: 位置{agv.position}, 载重{agv.current_load}/{agv.max_capacity}, "
                      f"任务{agv.carrying_tasks}")

    def close(self):
        """关闭环境"""
        pass


if __name__ == "__main__":
    # 测试环境
    env = MultiAGVEnv()

    # 重置环境
    obs, info = env.reset()
    print("初始观测形状:", {k: v.shape for k, v in obs.items()})

    # 执行几步
    for step in range(5):
        # 随机动作
        actions = {}
        for agent_id in env.agent_ids:
            task_action = np.random.randint(0, ACTION_CONFIG.TASK_ACTION_DIM)
            motion_action = np.random.randint(0, ACTION_CONFIG.MOTION_ACTION_DIM)
            actions[agent_id] = [task_action, motion_action]

        obs, rewards, terminateds, truncateds, infos = env.step(actions)

        print(f"\nStep {step + 1}:")
        print("奖励:", rewards)
        print("终止:", terminateds)

        env.render()

        if terminateds.get("__all__", False):
            print("所有任务完成!")
            break
