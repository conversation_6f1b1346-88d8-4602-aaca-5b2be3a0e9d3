# 基于单层AGV-Task-AGV注意力机制的MAPPO多AGV调度优化系统

## 项目概述

本项目实现了一个基于单层AGV-Task-AGV注意力机制的MAPPO多AGV调度优化系统，严格按照研究方案`simplified_research_methodology.md`构建。系统实现了多载重AGV的自主任务选择、载重优化、路径平衡和协作调度。

## 核心创新

### 🔥 技术创新点

1. **单层AGV-Task-AGV注意力机制**：统一处理任务分配和协作感知的异构图注意力架构
2. **约束增强异构图注意力**：融合物理约束和业务约束的统一注意力计算
3. **MAPPO深度融合**：将统一注意力机制集成到策略网络和价值网络中
4. **渐进式课程学习**：6阶段渐进式学习策略，从简单到复杂
5. **Top-K稀疏化优化**：显著降低计算复杂度的稀疏注意力机制

### 🎯 实现目标

- **自主任务选择**：多个AGV基于注意力机制自主选择最适合的任务
- **载重优化**：最大化载重利用率，支持多任务同时携带
- **路径平衡**：在载重优化和路径效率间找到最优平衡点
- **协作调度**：多AGV间智能协作，避免冲突和资源竞争

## 系统架构

```
├── envs/                           # 环境层
│   ├── multi_agv_env.py           # 多AGV环境（基于Ray RLlib）
│   ├── map_manager.py             # 地图管理器（26×10网格世界）
│   ├── task_manager.py            # 任务管理器（16个任务）
│   └── agv_manager.py             # AGV管理器（4个AGV）
├── models/                         # 模型层
│   ├── attention_mechanism.py     # 单层AGV-Task-AGV注意力机制
│   ├── mappo_with_attention.py    # MAPPO算法集成
│   ├── autonomous_task_selection.py # 自主任务选择器
│   ├── load_optimization.py       # 载重优化器
│   ├── path_load_balance.py       # 路径-载重平衡管理器
│   ├── multi_task_carrying.py     # 多任务携带管理器
│   ├── multi_objective_reward.py  # 多目标奖励系统
│   └── curriculum_learning.py     # 渐进式课程学习管理器
├── utils/                          # 工具层
│   └── config.py                  # 系统配置
├── train_mappo_attention.py       # 训练脚本
├── evaluate_system.py             # 评估脚本
└── main.py                        # 主入口文件
```

## 环境规格

### 物理环境配置
- **地图规格**：26×10的离散网格地图
- **货架分布**：16个货架位置，每个货架可放置一个任务
- **卸货点**：2个卸货位置，分别位于地图左上角(0,0)和右上角(25,0)
- **AGV配置**：4个AGV，编号为0-3，最大载重25单位
- **任务配置**：16个任务，重量为5或10单位

### 状态空间设计（77维）
- **AGV局部观测**（5维）：位置归一化、载重比例、目标卸货点、携带任务数量
- **任务全局状态**（64维）：16×4维，包含位置、重量、状态、目标信息
- **环境全局状态**（8维）：完成率、活跃任务数、载重利用率等系统指标

### 动作空间设计
- **任务选择动作**（18维）：16个任务 + 保持当前 + 前往卸货
- **运动控制动作**（5维）：上、下、左、右、静止

## 快速开始

### 环境要求

```bash
Python >= 3.8
PyTorch >= 1.9.0
Ray[rllib] >= 2.0.0
NumPy >= 1.21.0
Matplotlib >= 3.5.0
Seaborn >= 0.11.0
```

### 安装依赖

```bash
pip install torch torchvision
pip install "ray[rllib]" 
pip install numpy matplotlib seaborn
pip install gymnasium
```

### 运行系统

#### 1. 系统分析
```bash
python main.py analysis
```

#### 2. 运行演示
```bash
python main.py demo
```

#### 3. 开始训练
```bash
python main.py train
```

#### 4. 评估系统
```bash
# 运行对比评估
python main.py eval

# 评估指定模型
python main.py eval --model checkpoints/model.pkl --episodes 200
```

## 课程学习阶段

系统实现了6阶段渐进式课程学习：

1. **阶段1**（Episode 0-500）：基础移动学习
   - 1个AGV，4个任务，固定重量5单位
   - 成功标准：任务完成率≥80%，路径效率≥70%，碰撞率≤5%

2. **阶段2**（Episode 500-1500）：单AGV多任务学习
   - 1个AGV，8个任务，重量1-10单位随机
   - 成功标准：载重利用率≥60%，多任务完成率≥50%

3. **阶段3**（Episode 1500-3000）：双AGV协作学习
   - 2个AGV，12个任务，重量1-12单位随机
   - 成功标准：协作成功率≥70%，碰撞率≤3%，负载均衡度≥60%

4. **阶段4**（Episode 3000-5000）：多AGV复杂协作
   - 3个AGV，14个任务，重量1-15单位随机
   - 成功标准：系统任务完成率≥65%，载重利用率≥70%，协作效率≥75%

5. **阶段5**（Episode 5000-7500）：满载协作优化
   - 4个AGV，16个任务，重量1-15单位随机
   - 成功标准：系统任务完成率≥70%，载重利用率≥80%，路径效率≥85%，碰撞率≤2%

6. **阶段6**（Episode 7500-10000）：动态环境适应
   - 动态任务生成、动态权重变化、环境扰动
   - 成功标准：动态适应能力≥80%，鲁棒性指标≥75%

## 性能指标

### 任务执行指标
- **任务完成率**：完成任务数/总任务数
- **平均完成时间**：任务完成的平均时间步数
- **任务分配效率**：最优分配数/总分配数

### 载重优化指标
- **载重利用率**：实际载重/最大载重容量
- **多任务携带率**：多任务episode数/总episode数
- **载重均衡度**：1 - 载重利用率标准差/平均值

### 路径效率指标
- **路径最优性**：最优路径长度/实际路径长度
- **移动效率**：有效移动数/总移动数
- **拥堵避免率**：成功避让数/潜在拥堵数

### 协作性能指标
- **协作成功率**：成功协作数/协作机会数
- **碰撞率**：碰撞次数/总交互次数
- **决策一致性**：一致决策数/总决策数

## 预期性能目标

- **任务完成率**：≥ 70%（相比贪心算法提升20%）
- **载重利用率**：≥ 80%（相比标准MAPPO提升25%）
- **路径最优性**：≥ 85%
- **碰撞率**：≤ 2%（相比独立学习降低80%）
- **协作成功率**：≥ 90%

## 文件说明

### 核心文件
- `simplified_research_methodology.md`：完整研究方案文档（**不可删除**）
- `main.py`：统一入口点，提供训练、评估、演示、分析功能
- `train_mappo_attention.py`：完整的MAPPO训练流程，集成课程学习
- `evaluate_system.py`：评估指标体系和对比分析

### 环境文件
- `envs/multi_agv_env.py`：基于Ray RLlib的多智能体环境
- `envs/map_manager.py`：26×10网格地图管理
- `envs/task_manager.py`：16个任务的生成和状态管理
- `envs/agv_manager.py`：4个AGV的状态和行为管理

### 模型文件
- `models/attention_mechanism.py`：核心创新-单层AGV-Task-AGV注意力机制
- `models/mappo_with_attention.py`：策略网络、价值网络、损失函数
- `models/curriculum_learning.py`：6阶段渐进式课程学习管理器
- `models/autonomous_task_selection.py`：自主任务选择和载重优化
- `models/load_optimization.py`：载重利用率最大化和动态负载均衡
- `models/path_load_balance.py`：路径效率与载重利用率权衡优化
- `models/multi_task_carrying.py`：多任务同时携带决策管理
- `models/multi_objective_reward.py`：多目标奖励系统

## 注意事项

1. **研究方案文档**：`simplified_research_methodology.md`是核心参考文档，不得删除
2. **测试文件管理**：测试文件在测试完成后需要及时清理
3. **非必要文档**：除研究方案外，不创建额外的总结性文档
4. **编译运行**：用户自行编译和运行，系统不自动执行

## 技术支持

本项目严格按照研究方案实现，包含完整的多AGV调度优化系统。如有技术问题，请参考：

1. 研究方案文档：`simplified_research_methodology.md`
2. 系统分析：`python main.py analysis`
3. 演示运行：`python main.py demo`

## 许可证

本项目仅用于学术研究目的。
