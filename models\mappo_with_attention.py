"""
MAPPO算法集成 - 基于单层AGV-Task-AGV注意力机制的MAPPO多AGV调度优化系统
实现研究方案第5章的MAPPO算法集成
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import List, Dict, Optional, Tuple, Any
from utils.config import MAPPO_CONFIG, STATE_CONFIG, ACTION_CONFIG, ATTENTION_CONFIG
from models.attention_mechanism import SingleLayerAGVTaskAGVAttention


class PolicyNetwork(nn.Module):
    """策略网络 - 集成单层AGV-Task-AGV注意力机制
    
    实现研究方案中的策略网络设计：
    1. 输入层：AGV局部观测(5) + 全局任务状态(64) + 环境全局状态(8) = 77维
    2. 特征提取层：局部观测编码、任务状态编码、环境状态编码、特征融合
    3. 注意力层：AGV-Task-AGV异构图注意力机制
    4. 决策层：任务选择头、运动控制头
    """
    
    def __init__(self):
        super().__init__()
        
        # 特征提取层
        self.agv_encoder = nn.Linear(STATE_CONFIG.AGV_STATE_DIM, 32)
        self.task_encoder = nn.Linear(STATE_CONFIG.TOTAL_TASK_STATE_DIM, 64)
        self.env_encoder = nn.Linear(STATE_CONFIG.GLOBAL_STATE_DIM, 16)
        self.feature_fusion = nn.Linear(32 + 64 + 16, MAPPO_CONFIG.FEATURE_EXTRACT_DIM)
        
        # 任务特征编码器（用于注意力机制）
        self.task_feature_proj = nn.Linear(STATE_CONFIG.TASK_STATE_DIM, ATTENTION_CONFIG.ATTENTION_DIM)
        
        # 单层AGV-Task-AGV注意力机制
        self.attention_mechanism = SingleLayerAGVTaskAGVAttention(ATTENTION_CONFIG.ATTENTION_DIM)
        
        # 决策层
        self.task_head = nn.Sequential(
            nn.Linear(ATTENTION_CONFIG.ATTENTION_DIM, MAPPO_CONFIG.POLICY_HIDDEN_DIM),
            nn.ReLU(),
            nn.Linear(MAPPO_CONFIG.POLICY_HIDDEN_DIM, ACTION_CONFIG.TASK_ACTION_DIM)
        )
        
        self.motion_head = nn.Sequential(
            nn.Linear(ATTENTION_CONFIG.ATTENTION_DIM, MAPPO_CONFIG.POLICY_HIDDEN_DIM),
            nn.ReLU(),
            nn.Linear(MAPPO_CONFIG.POLICY_HIDDEN_DIM, ACTION_CONFIG.MOTION_ACTION_DIM)
        )
        
        # 初始化权重
        self._initialize_weights()
    
    def forward(self, observations: torch.Tensor, 
                agv_positions: torch.Tensor, task_positions: torch.Tensor,
                agv_loads: torch.Tensor, task_weights: torch.Tensor,
                task_status: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor, Dict]:
        """前向传播
        
        Args:
            observations: 观测 [batch, num_agvs, obs_dim]
            agv_positions: AGV位置 [batch, num_agvs, 2]
            task_positions: 任务位置 [batch, num_tasks, 2]
            agv_loads: AGV载重 [batch, num_agvs]
            task_weights: 任务重量 [batch, num_tasks]
            task_status: 任务状态 [batch, num_tasks]
            
        Returns:
            (任务动作logits, 运动动作logits, 注意力信息)
        """
        batch_size, num_agvs, obs_dim = observations.shape
        
        # 1. 特征提取
        agv_features = self._extract_features(observations)  # [batch, num_agvs, feature_dim]
        
        # 2. 任务特征编码
        task_features = self._encode_task_features(observations, task_positions, 
                                                 task_weights, task_status)  # [batch, num_tasks, attention_dim]
        
        # 3. 注意力机制
        attention_output = self.attention_mechanism(
            agv_features, task_features, agv_positions, task_positions,
            agv_loads, task_weights, task_status
        )
        
        enhanced_features = attention_output.enhanced_features  # [batch, num_agvs, attention_dim]
        
        # 4. 策略输出
        task_logits = self.task_head(enhanced_features)    # [batch, num_agvs, task_action_dim]
        motion_logits = self.motion_head(enhanced_features)  # [batch, num_agvs, motion_action_dim]
        
        # 5. 注意力信息
        attention_info = {
            'task_attention_weights': attention_output.task_attention_weights,
            'agv_attention_weights': attention_output.agv_attention_weights,
            'attention_scores': attention_output.attention_scores
        }
        
        return task_logits, motion_logits, attention_info
    
    def _extract_features(self, observations: torch.Tensor) -> torch.Tensor:
        """提取特征"""
        batch_size, num_agvs, obs_dim = observations.shape
        
        # 分解观测
        agv_obs = observations[:, :, :STATE_CONFIG.AGV_STATE_DIM]
        task_obs = observations[:, :, STATE_CONFIG.AGV_STATE_DIM:STATE_CONFIG.AGV_STATE_DIM + STATE_CONFIG.TOTAL_TASK_STATE_DIM]
        env_obs = observations[:, :, -STATE_CONFIG.GLOBAL_STATE_DIM:]
        
        # 编码各部分
        agv_encoded = F.relu(self.agv_encoder(agv_obs))
        task_encoded = F.relu(self.task_encoder(task_obs))
        env_encoded = F.relu(self.env_encoder(env_obs))
        
        # 特征融合
        combined = torch.cat([agv_encoded, task_encoded, env_encoded], dim=-1)
        features = F.relu(self.feature_fusion(combined))
        
        return features
    
    def _encode_task_features(self, observations: torch.Tensor, task_positions: torch.Tensor,
                            task_weights: torch.Tensor, task_status: torch.Tensor) -> torch.Tensor:
        """编码任务特征用于注意力机制"""
        batch_size = observations.shape[0]
        num_tasks = task_positions.shape[1]
        
        # 从观测中提取任务状态
        task_obs = observations[:, 0, STATE_CONFIG.AGV_STATE_DIM:STATE_CONFIG.AGV_STATE_DIM + STATE_CONFIG.TOTAL_TASK_STATE_DIM]
        task_obs = task_obs.view(batch_size, num_tasks, STATE_CONFIG.TASK_STATE_DIM)
        
        # 投影到注意力维度
        task_features = self.task_feature_proj(task_obs)
        
        return task_features
    
    def _initialize_weights(self):
        """初始化网络权重"""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.orthogonal_(module.weight, gain=np.sqrt(2))
                nn.init.constant_(module.bias, 0)


class ValueNetwork(nn.Module):
    """价值网络 - 中心化价值估计
    
    实现研究方案中的价值网络设计：
    1. 输入信息：全局状态(92维) + 注意力特征(256维)
    2. 价值函数设计：系统级价值评估 + 个体AGV价值贡献
    3. 网络结构：全局特征提取 + 注意力特征编码 + 价值估计
    """
    
    def __init__(self):
        super().__init__()
        
        # 全局状态维度：4×AGV状态(5) + 16×任务状态(4) + 环境状态(8) = 92
        global_state_dim = 4 * STATE_CONFIG.AGV_STATE_DIM + 16 * STATE_CONFIG.TASK_STATE_DIM + STATE_CONFIG.GLOBAL_STATE_DIM
        
        # 注意力特征维度：4×AGV注意力输出(64) = 256
        attention_feature_dim = 4 * ATTENTION_CONFIG.ATTENTION_DIM
        
        # 全局特征提取
        self.global_encoder = nn.Sequential(
            nn.Linear(global_state_dim, 128),
            nn.ReLU(),
            nn.Linear(128, 64)
        )
        
        # 注意力特征编码
        self.attention_encoder = nn.Sequential(
            nn.Linear(attention_feature_dim, 128),
            nn.ReLU(),
            nn.Linear(128, 64)
        )
        
        # 特征融合
        self.feature_fusion = nn.Linear(128, 64)
        
        # 价值估计
        self.value_head = nn.Sequential(
            nn.Linear(64, MAPPO_CONFIG.VALUE_HIDDEN_DIM),
            nn.ReLU(),
            nn.Linear(MAPPO_CONFIG.VALUE_HIDDEN_DIM, 32),
            nn.ReLU(),
            nn.Linear(32, 1)
        )
        
        # 初始化权重
        self._initialize_weights()
    
    def forward(self, global_state: torch.Tensor, 
                attention_features: torch.Tensor) -> torch.Tensor:
        """前向传播
        
        Args:
            global_state: 全局状态 [batch, global_state_dim]
            attention_features: 注意力特征 [batch, attention_feature_dim]
            
        Returns:
            价值估计 [batch, 1]
        """
        # 编码全局状态
        global_encoded = self.global_encoder(global_state)
        
        # 编码注意力特征
        attention_encoded = self.attention_encoder(attention_features)
        
        # 特征融合
        combined = torch.cat([global_encoded, attention_encoded], dim=-1)
        fused_features = F.relu(self.feature_fusion(combined))
        
        # 价值估计
        value = self.value_head(fused_features)
        
        return value
    
    def _initialize_weights(self):
        """初始化网络权重"""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.orthogonal_(module.weight, gain=np.sqrt(2))
                nn.init.constant_(module.bias, 0)


class MAPPOLoss(nn.Module):
    """MAPPO损失函数
    
    实现研究方案中的损失函数设计：
    1. PPO策略损失：任务选择损失 + 运动控制损失
    2. 价值函数损失
    3. 熵正则化损失
    4. 注意力正则化损失：稀疏性损失 + 平滑性损失
    """
    
    def __init__(self):
        super().__init__()
        self.clip_epsilon = MAPPO_CONFIG.CLIP_EPSILON
        self.entropy_coeff = MAPPO_CONFIG.ENTROPY_COEFF
        self.value_coeff = MAPPO_CONFIG.VALUE_COEFF
        self.sparse_loss_weight = MAPPO_CONFIG.SPARSE_LOSS_WEIGHT
        self.smooth_loss_weight = MAPPO_CONFIG.SMOOTH_LOSS_WEIGHT
    
    def forward(self, task_logits: torch.Tensor, motion_logits: torch.Tensor,
                task_actions: torch.Tensor, motion_actions: torch.Tensor,
                old_task_log_probs: torch.Tensor, old_motion_log_probs: torch.Tensor,
                advantages: torch.Tensor, values: torch.Tensor, returns: torch.Tensor,
                attention_weights: Dict[str, torch.Tensor],
                prev_attention_weights: Optional[Dict[str, torch.Tensor]] = None) -> Dict[str, torch.Tensor]:
        """计算MAPPO损失
        
        Args:
            task_logits: 任务动作logits
            motion_logits: 运动动作logits
            task_actions: 任务动作
            motion_actions: 运动动作
            old_task_log_probs: 旧任务动作对数概率
            old_motion_log_probs: 旧运动动作对数概率
            advantages: 优势函数
            values: 价值估计
            returns: 回报
            attention_weights: 注意力权重
            prev_attention_weights: 前一步注意力权重
            
        Returns:
            损失字典
        """
        # 1. 计算新的动作概率
        task_probs = F.softmax(task_logits, dim=-1)
        motion_probs = F.softmax(motion_logits, dim=-1)
        
        task_log_probs = F.log_softmax(task_logits, dim=-1)
        motion_log_probs = F.log_softmax(motion_logits, dim=-1)
        
        # 选择对应动作的对数概率
        new_task_log_probs = task_log_probs.gather(-1, task_actions.unsqueeze(-1)).squeeze(-1)
        new_motion_log_probs = motion_log_probs.gather(-1, motion_actions.unsqueeze(-1)).squeeze(-1)
        
        # 2. PPO策略损失
        task_ratio = torch.exp(new_task_log_probs - old_task_log_probs)
        motion_ratio = torch.exp(new_motion_log_probs - old_motion_log_probs)
        
        # 任务选择损失
        task_surr1 = task_ratio * advantages
        task_surr2 = torch.clamp(task_ratio, 1 - self.clip_epsilon, 1 + self.clip_epsilon) * advantages
        task_policy_loss = -torch.min(task_surr1, task_surr2).mean()
        
        # 运动控制损失
        motion_surr1 = motion_ratio * advantages
        motion_surr2 = torch.clamp(motion_ratio, 1 - self.clip_epsilon, 1 + self.clip_epsilon) * advantages
        motion_policy_loss = -torch.min(motion_surr1, motion_surr2).mean()
        
        total_policy_loss = task_policy_loss + motion_policy_loss
        
        # 3. 价值函数损失
        value_loss = F.mse_loss(values.squeeze(-1), returns)
        
        # 4. 熵正则化损失
        task_entropy = -(task_probs * task_log_probs).sum(dim=-1).mean()
        motion_entropy = -(motion_probs * motion_log_probs).sum(dim=-1).mean()
        entropy_loss = -(task_entropy + motion_entropy)
        
        # 5. 注意力正则化损失
        sparse_loss = self._calculate_sparse_loss(attention_weights)
        smooth_loss = self._calculate_smooth_loss(attention_weights, prev_attention_weights)
        
        # 6. 总损失
        total_loss = (total_policy_loss + 
                     self.value_coeff * value_loss + 
                     self.entropy_coeff * entropy_loss +
                     self.sparse_loss_weight * sparse_loss +
                     self.smooth_loss_weight * smooth_loss)
        
        return {
            'total_loss': total_loss,
            'policy_loss': total_policy_loss,
            'task_policy_loss': task_policy_loss,
            'motion_policy_loss': motion_policy_loss,
            'value_loss': value_loss,
            'entropy_loss': entropy_loss,
            'sparse_loss': sparse_loss,
            'smooth_loss': smooth_loss
        }
    
    def _calculate_sparse_loss(self, attention_weights: Dict[str, torch.Tensor]) -> torch.Tensor:
        """计算注意力稀疏性损失"""
        sparse_loss = 0.0
        
        if 'task_attention_weights' in attention_weights:
            task_weights = attention_weights['task_attention_weights']
            sparse_loss += torch.sum(torch.abs(task_weights))
        
        if 'agv_attention_weights' in attention_weights:
            agv_weights = attention_weights['agv_attention_weights']
            sparse_loss += torch.sum(torch.abs(agv_weights))
        
        return sparse_loss
    
    def _calculate_smooth_loss(self, attention_weights: Dict[str, torch.Tensor],
                             prev_attention_weights: Optional[Dict[str, torch.Tensor]]) -> torch.Tensor:
        """计算注意力平滑性损失"""
        if prev_attention_weights is None:
            return torch.tensor(0.0, device=attention_weights['task_attention_weights'].device)
        
        smooth_loss = 0.0
        
        if ('task_attention_weights' in attention_weights and 
            'task_attention_weights' in prev_attention_weights):
            curr_weights = attention_weights['task_attention_weights']
            prev_weights = prev_attention_weights['task_attention_weights']
            smooth_loss += torch.sum((curr_weights - prev_weights) ** 2)
        
        if ('agv_attention_weights' in attention_weights and 
            'agv_attention_weights' in prev_attention_weights):
            curr_weights = attention_weights['agv_attention_weights']
            prev_weights = prev_attention_weights['agv_attention_weights']
            smooth_loss += torch.sum((curr_weights - prev_weights) ** 2)
        
        return smooth_loss
