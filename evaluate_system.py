"""
系统评估脚本 - 基于单层AGV-Task-AGV注意力机制的MAPPO多AGV调度优化系统
实现研究方案第8章的评估指标体系和对比分析
"""

import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple, Any, Optional
from dataclasses import dataclass
import json
import os

from envs.multi_agv_env import MultiAGVEnv
from utils.config import ENV_CONFIG, MAPPO_CONFIG


@dataclass
class EvaluationMetrics:
    """评估指标"""
    # 任务执行指标
    task_completion_rate: float
    avg_completion_time: float
    task_allocation_efficiency: float
    
    # 载重优化指标
    load_utilization: float
    multi_task_rate: float
    load_balance: float
    
    # 路径效率指标
    path_optimality: float
    movement_efficiency: float
    congestion_avoidance_rate: float
    
    # 协作性能指标
    cooperation_success_rate: float
    collision_rate: float
    decision_consistency: float
    
    # 综合指标
    overall_performance: float
    system_efficiency: float


class SystemEvaluator:
    """系统评估器"""
    
    def __init__(self, env_config: Dict = None):
        """初始化评估器"""
        self.env_config = env_config or {}
        self.evaluation_results = []
        
    def evaluate_model(self, model_path: str, num_episodes: int = 100) -> EvaluationMetrics:
        """评估模型性能
        
        Args:
            model_path: 模型路径
            num_episodes: 评估episode数量
            
        Returns:
            评估指标
        """
        print(f"开始评估模型: {model_path}")
        
        # 创建环境
        env = MultiAGVEnv(self.env_config)
        
        # 加载模型（这里需要根据实际的模型加载方式调整）
        # model = self._load_model(model_path)
        
        episode_metrics = []
        
        for episode in range(num_episodes):
            # 运行一个episode
            episode_result = self._run_episode(env)
            episode_metrics.append(episode_result)
            
            if episode % 20 == 0:
                print(f"评估进度: {episode}/{num_episodes}")
        
        # 计算平均指标
        avg_metrics = self._calculate_average_metrics(episode_metrics)
        
        print("评估完成！")
        return avg_metrics
    
    def _run_episode(self, env: MultiAGVEnv) -> Dict[str, float]:
        """运行一个评估episode"""
        obs, info = env.reset()
        
        episode_metrics = {
            'total_reward': 0.0,
            'episode_length': 0,
            'tasks_completed': 0,
            'total_tasks': ENV_CONFIG.NUM_TASKS,
            'collisions': 0,
            'total_load_utilization': 0.0,
            'path_lengths': [],
            'cooperation_events': 0,
            'successful_cooperations': 0
        }
        
        done = False
        step = 0
        
        while not done and step < ENV_CONFIG.MAX_EPISODE_STEPS:
            # 使用随机策略（在实际评估中应该使用训练好的模型）
            actions = self._get_random_actions(env)
            
            obs, rewards, terminateds, truncateds, infos = env.step(actions)
            
            # 更新指标
            episode_metrics['total_reward'] += sum(rewards.values())
            episode_metrics['episode_length'] = step + 1
            
            # 从info中提取详细指标
            if 'agv_0' in infos:
                info = infos['agv_0']
                episode_metrics['tasks_completed'] = info.get('task_stats', {}).get('completed_tasks', 0)
                episode_metrics['collisions'] = info.get('collision_count', 0)
                
                # 系统统计
                system_stats = info.get('system_stats', {})
                episode_metrics['total_load_utilization'] = system_stats.get('average_load_utilization', 0.0)
            
            # 检查终止条件
            done = terminateds.get("__all__", False) or truncateds.get("__all__", False)
            step += 1
        
        return episode_metrics
    
    def _get_random_actions(self, env: MultiAGVEnv) -> Dict[str, List[int]]:
        """获取随机动作（用于测试）"""
        actions = {}
        for agent_id in env.agent_ids:
            # 获取动作掩码
            action_mask = env.get_action_mask(agent_id)
            
            # 任务选择动作
            task_mask = action_mask['task_action_mask']
            valid_task_actions = np.where(task_mask)[0]
            task_action = np.random.choice(valid_task_actions) if len(valid_task_actions) > 0 else 0
            
            # 运动控制动作
            motion_mask = action_mask['motion_action_mask']
            valid_motion_actions = np.where(motion_mask)[0]
            motion_action = np.random.choice(valid_motion_actions) if len(valid_motion_actions) > 0 else 0
            
            actions[agent_id] = [task_action, motion_action]
        
        return actions
    
    def _calculate_average_metrics(self, episode_metrics: List[Dict[str, float]]) -> EvaluationMetrics:
        """计算平均评估指标"""
        if not episode_metrics:
            return EvaluationMetrics(0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0)
        
        # 任务执行指标
        task_completion_rates = [m['tasks_completed'] / m['total_tasks'] for m in episode_metrics]
        task_completion_rate = np.mean(task_completion_rates)
        
        avg_completion_time = np.mean([m['episode_length'] for m in episode_metrics])
        
        # 简化的任务分配效率计算
        task_allocation_efficiency = task_completion_rate * 0.8  # 简化计算
        
        # 载重优化指标
        load_utilization = np.mean([m['total_load_utilization'] for m in episode_metrics])
        
        # 简化的多任务率计算
        multi_task_rate = min(1.0, load_utilization * 1.2)  # 简化计算
        
        # 负载均衡度（简化计算）
        load_balance = max(0, 1.0 - np.std([m['total_load_utilization'] for m in episode_metrics]))
        
        # 路径效率指标（简化计算）
        path_optimality = 0.8  # 需要更复杂的计算
        movement_efficiency = task_completion_rate * 0.9  # 简化计算
        congestion_avoidance_rate = 0.85  # 需要更复杂的计算
        
        # 协作性能指标
        collision_rates = [m['collisions'] / max(1, m['episode_length']) for m in episode_metrics]
        collision_rate = np.mean(collision_rates)
        
        cooperation_success_rate = 0.8  # 需要更复杂的计算
        decision_consistency = 0.85  # 需要更复杂的计算
        
        # 综合指标
        overall_performance = (
            0.3 * task_completion_rate +
            0.25 * load_utilization +
            0.2 * path_optimality +
            0.15 * cooperation_success_rate +
            0.1 * (1.0 - collision_rate)
        )
        
        system_efficiency = overall_performance * load_balance
        
        return EvaluationMetrics(
            task_completion_rate=task_completion_rate,
            avg_completion_time=avg_completion_time,
            task_allocation_efficiency=task_allocation_efficiency,
            load_utilization=load_utilization,
            multi_task_rate=multi_task_rate,
            load_balance=load_balance,
            path_optimality=path_optimality,
            movement_efficiency=movement_efficiency,
            congestion_avoidance_rate=congestion_avoidance_rate,
            cooperation_success_rate=cooperation_success_rate,
            collision_rate=collision_rate,
            decision_consistency=decision_consistency,
            overall_performance=overall_performance,
            system_efficiency=system_efficiency
        )
    
    def compare_methods(self, method_results: Dict[str, EvaluationMetrics]) -> Dict[str, Any]:
        """对比不同方法的性能
        
        Args:
            method_results: 方法名 -> 评估指标的字典
            
        Returns:
            对比分析结果
        """
        comparison = {
            'method_names': list(method_results.keys()),
            'metrics_comparison': {},
            'performance_ranking': {},
            'improvement_analysis': {}
        }
        
        # 提取所有指标
        metric_names = [
            'task_completion_rate', 'load_utilization', 'path_optimality',
            'cooperation_success_rate', 'collision_rate', 'overall_performance'
        ]
        
        for metric in metric_names:
            values = [getattr(result, metric) for result in method_results.values()]
            comparison['metrics_comparison'][metric] = {
                'values': dict(zip(method_results.keys(), values)),
                'best_method': max(method_results.keys(), key=lambda k: getattr(method_results[k], metric)),
                'improvement': max(values) - min(values) if values else 0
            }
        
        # 性能排名
        for method_name, metrics in method_results.items():
            comparison['performance_ranking'][method_name] = metrics.overall_performance
        
        # 改进分析
        if 'Our Method' in method_results:
            our_metrics = method_results['Our Method']
            for method_name, metrics in method_results.items():
                if method_name != 'Our Method':
                    improvements = {}
                    for metric in metric_names:
                        our_value = getattr(our_metrics, metric)
                        other_value = getattr(metrics, metric)
                        if metric == 'collision_rate':  # 越低越好
                            improvement = (other_value - our_value) / other_value * 100 if other_value > 0 else 0
                        else:  # 越高越好
                            improvement = (our_value - other_value) / other_value * 100 if other_value > 0 else 0
                        improvements[metric] = improvement
                    
                    comparison['improvement_analysis'][method_name] = improvements
        
        return comparison
    
    def generate_evaluation_report(self, results: Dict[str, EvaluationMetrics], 
                                 comparison: Dict[str, Any], output_dir: str = "evaluation_results"):
        """生成评估报告"""
        os.makedirs(output_dir, exist_ok=True)
        
        # 保存数值结果
        report_data = {
            'evaluation_results': {
                method: {
                    'task_completion_rate': metrics.task_completion_rate,
                    'load_utilization': metrics.load_utilization,
                    'path_optimality': metrics.path_optimality,
                    'cooperation_success_rate': metrics.cooperation_success_rate,
                    'collision_rate': metrics.collision_rate,
                    'overall_performance': metrics.overall_performance
                }
                for method, metrics in results.items()
            },
            'comparison_analysis': comparison
        }
        
        with open(os.path.join(output_dir, 'evaluation_report.json'), 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)
        
        # 生成可视化图表
        self._generate_visualization(results, comparison, output_dir)
        
        print(f"评估报告已生成到: {output_dir}")
    
    def _generate_visualization(self, results: Dict[str, EvaluationMetrics], 
                              comparison: Dict[str, Any], output_dir: str):
        """生成可视化图表"""
        plt.style.use('seaborn-v0_8')
        
        # 1. 性能对比雷达图
        self._plot_radar_chart(results, output_dir)
        
        # 2. 指标对比柱状图
        self._plot_metrics_comparison(results, output_dir)
        
        # 3. 改进分析图
        if comparison.get('improvement_analysis'):
            self._plot_improvement_analysis(comparison['improvement_analysis'], output_dir)
    
    def _plot_radar_chart(self, results: Dict[str, EvaluationMetrics], output_dir: str):
        """绘制性能对比雷达图"""
        metrics = ['任务完成率', '载重利用率', '路径最优性', '协作成功率', '系统效率']
        
        fig, ax = plt.subplots(figsize=(10, 8), subplot_kw=dict(projection='polar'))
        
        angles = np.linspace(0, 2 * np.pi, len(metrics), endpoint=False).tolist()
        angles += angles[:1]  # 闭合图形
        
        for method_name, method_metrics in results.items():
            values = [
                method_metrics.task_completion_rate,
                method_metrics.load_utilization,
                method_metrics.path_optimality,
                method_metrics.cooperation_success_rate,
                method_metrics.system_efficiency
            ]
            values += values[:1]  # 闭合图形
            
            ax.plot(angles, values, 'o-', linewidth=2, label=method_name)
            ax.fill(angles, values, alpha=0.25)
        
        ax.set_xticks(angles[:-1])
        ax.set_xticklabels(metrics)
        ax.set_ylim(0, 1)
        ax.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))
        ax.set_title('多AGV调度系统性能对比', size=16, pad=20)
        
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'performance_radar.png'), dpi=300, bbox_inches='tight')
        plt.close()
    
    def _plot_metrics_comparison(self, results: Dict[str, EvaluationMetrics], output_dir: str):
        """绘制指标对比柱状图"""
        metrics_data = {
            '任务完成率': [m.task_completion_rate for m in results.values()],
            '载重利用率': [m.load_utilization for m in results.values()],
            '路径最优性': [m.path_optimality for m in results.values()],
            '协作成功率': [m.cooperation_success_rate for m in results.values()],
            '碰撞率': [m.collision_rate for m in results.values()]
        }
        
        x = np.arange(len(results))
        width = 0.15
        
        fig, ax = plt.subplots(figsize=(12, 8))
        
        for i, (metric, values) in enumerate(metrics_data.items()):
            offset = (i - 2) * width
            bars = ax.bar(x + offset, values, width, label=metric)
            
            # 添加数值标签
            for bar, value in zip(bars, values):
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                       f'{value:.3f}', ha='center', va='bottom', fontsize=8)
        
        ax.set_xlabel('方法')
        ax.set_ylabel('性能指标')
        ax.set_title('多AGV调度系统指标对比')
        ax.set_xticks(x)
        ax.set_xticklabels(results.keys())
        ax.legend()
        ax.set_ylim(0, 1.1)
        
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'metrics_comparison.png'), dpi=300, bbox_inches='tight')
        plt.close()
    
    def _plot_improvement_analysis(self, improvement_data: Dict[str, Dict[str, float]], output_dir: str):
        """绘制改进分析图"""
        methods = list(improvement_data.keys())
        metrics = list(improvement_data[methods[0]].keys())
        
        fig, ax = plt.subplots(figsize=(12, 8))
        
        x = np.arange(len(methods))
        width = 0.12
        
        for i, metric in enumerate(metrics):
            values = [improvement_data[method][metric] for method in methods]
            offset = (i - len(metrics)/2) * width
            bars = ax.bar(x + offset, values, width, label=metric)
            
            # 添加数值标签
            for bar, value in zip(bars, values):
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height + 1,
                       f'{value:.1f}%', ha='center', va='bottom', fontsize=8)
        
        ax.set_xlabel('基准方法')
        ax.set_ylabel('改进百分比 (%)')
        ax.set_title('相对于基准方法的性能改进')
        ax.set_xticks(x)
        ax.set_xticklabels(methods)
        ax.legend()
        ax.axhline(y=0, color='black', linestyle='-', alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'improvement_analysis.png'), dpi=300, bbox_inches='tight')
        plt.close()


def main():
    """主函数 - 演示评估流程"""
    evaluator = SystemEvaluator()
    
    # 模拟不同方法的评估结果
    results = {
        'Our Method (MAPPO+Attention)': EvaluationMetrics(
            task_completion_rate=0.85, avg_completion_time=280, task_allocation_efficiency=0.82,
            load_utilization=0.78, multi_task_rate=0.65, load_balance=0.88,
            path_optimality=0.83, movement_efficiency=0.79, congestion_avoidance_rate=0.91,
            cooperation_success_rate=0.87, collision_rate=0.03, decision_consistency=0.89,
            overall_performance=0.81, system_efficiency=0.76
        ),
        'Standard MAPPO': EvaluationMetrics(
            task_completion_rate=0.72, avg_completion_time=320, task_allocation_efficiency=0.68,
            load_utilization=0.61, multi_task_rate=0.45, load_balance=0.72,
            path_optimality=0.71, movement_efficiency=0.67, congestion_avoidance_rate=0.78,
            cooperation_success_rate=0.74, collision_rate=0.08, decision_consistency=0.76,
            overall_performance=0.68, system_efficiency=0.61
        ),
        'Independent PPO': EvaluationMetrics(
            task_completion_rate=0.58, avg_completion_time=380, task_allocation_efficiency=0.54,
            load_utilization=0.48, multi_task_rate=0.32, load_balance=0.58,
            path_optimality=0.62, movement_efficiency=0.55, congestion_avoidance_rate=0.65,
            cooperation_success_rate=0.61, collision_rate=0.15, decision_consistency=0.63,
            overall_performance=0.55, system_efficiency=0.48
        ),
        'Greedy Algorithm': EvaluationMetrics(
            task_completion_rate=0.45, avg_completion_time=420, task_allocation_efficiency=0.42,
            load_utilization=0.35, multi_task_rate=0.18, load_balance=0.45,
            path_optimality=0.58, movement_efficiency=0.48, congestion_avoidance_rate=0.52,
            cooperation_success_rate=0.48, collision_rate=0.22, decision_consistency=0.55,
            overall_performance=0.42, system_efficiency=0.38
        )
    }
    
    # 对比分析
    comparison = evaluator.compare_methods(results)
    
    # 生成评估报告
    evaluator.generate_evaluation_report(results, comparison)
    
    print("评估完成！请查看evaluation_results目录中的结果。")


if __name__ == "__main__":
    main()
