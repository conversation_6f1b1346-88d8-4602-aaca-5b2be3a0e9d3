"""
单层AGV-Task-AGV注意力机制 - 基于单层AGV-Task-AGV注意力机制的MAPPO多AGV调度优化系统
实现研究方案第4章的核心创新：统一的异构图注意力机制
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import List, Dict, Optional, Tuple, Any
from dataclasses import dataclass
from utils.config import ATTENTION_CONFIG, ENV_CONFIG


@dataclass
class AttentionOutput:
    """注意力输出"""
    enhanced_features: torch.Tensor  # 增强后的特征
    task_attention_weights: torch.Tensor  # 任务注意力权重
    agv_attention_weights: torch.Tensor  # AGV协作注意力权重
    attention_scores: Dict[str, float]  # 注意力评分


class ConstraintEnhancedAttention(nn.Module):
    """约束增强注意力模块
    
    实现研究方案中的约束增强机制：
    1. 任务分配约束：距离约束、载重约束、路径效率约束、任务状态约束
    2. 协作感知约束：载重均衡约束、距离分层约束、协作需求约束
    """
    
    def __init__(self, feature_dim: int = ATTENTION_CONFIG.ATTENTION_DIM):
        super().__init__()
        self.feature_dim = feature_dim
        
    def calculate_task_constraints(self, agv_features: torch.Tensor, task_features: torch.Tensor,
                                 agv_positions: torch.Tensor, task_positions: torch.Tensor,
                                 agv_loads: torch.Tensor, task_weights: torch.Tensor,
                                 task_status: torch.Tensor) -> torch.Tensor:
        """计算任务分配约束 - C_ij^task"""
        batch_size, num_agvs, _ = agv_features.shape
        _, num_tasks, _ = task_features.shape
        
        # 1. 距离约束 - C_distance^task(i,j)
        agv_pos_expanded = agv_positions.unsqueeze(2)  # [batch, num_agvs, 1, 2]
        task_pos_expanded = task_positions.unsqueeze(1)  # [batch, 1, num_tasks, 2]
        
        distances = torch.sum(torch.abs(agv_pos_expanded - task_pos_expanded), dim=-1)  # 曼哈顿距离
        max_distance = ENV_CONFIG.MAP_WIDTH + ENV_CONFIG.MAP_HEIGHT
        distance_constraints = -ATTENTION_CONFIG.DISTANCE_WEIGHT * (distances / max_distance)
        
        # 2. 载重约束 - C_load^task(i,j)
        agv_remaining = ENV_CONFIG.MAX_CAPACITY - agv_loads  # [batch, num_agvs]
        agv_remaining_expanded = agv_remaining.unsqueeze(2)  # [batch, num_agvs, 1]
        task_weights_expanded = task_weights.unsqueeze(1)  # [batch, 1, num_tasks]
        
        # 检查载重是否可行
        load_feasible = (task_weights_expanded <= agv_remaining_expanded).float()
        load_utilization = task_weights_expanded / (agv_remaining_expanded + 1e-8)
        load_constraints = torch.where(
            load_feasible > 0,
            ATTENTION_CONFIG.LOAD_WEIGHT * (1.0 - load_utilization),
            torch.full_like(load_utilization, -float('inf'))
        )
        
        # 3. 路径效率约束 - C_path^task(i,j)
        # 简化实现：基于距离的路径效率估计
        path_efficiency = 1.0 - (distances / max_distance)
        path_constraints = ATTENTION_CONFIG.PATH_WEIGHT * path_efficiency
        
        # 4. 任务状态约束 - C_status^task(i,j)
        # task_status: 0=available, 1=assigned, 2=completed
        status_constraints = torch.where(
            task_status.unsqueeze(1) == 0,  # 只有可用任务可以选择
            torch.zeros_like(distances),
            torch.full_like(distances, -float('inf'))
        )
        
        # 总任务约束
        total_constraints = (distance_constraints + load_constraints + 
                           path_constraints + status_constraints)
        
        return total_constraints
    
    def calculate_agv_constraints(self, agv_features: torch.Tensor, agv_positions: torch.Tensor,
                                agv_loads: torch.Tensor) -> torch.Tensor:
        """计算AGV协作约束 - C_ij^collab"""
        batch_size, num_agvs, _ = agv_features.shape
        
        # 1. 载重均衡约束 - C_load_balance^collab(i,j)
        agv_utilizations = agv_loads / ENV_CONFIG.MAX_CAPACITY  # [batch, num_agvs]
        util_i = agv_utilizations.unsqueeze(2)  # [batch, num_agvs, 1]
        util_j = agv_utilizations.unsqueeze(1)  # [batch, 1, num_agvs]
        
        util_diff = torch.abs(util_i - util_j)
        balance_constraints = ATTENTION_CONFIG.BALANCE_WEIGHT * (1.0 - util_diff)
        
        # 2. 距离分层约束 - C_distance^collab(i,j)
        pos_i = agv_positions.unsqueeze(2)  # [batch, num_agvs, 1, 2]
        pos_j = agv_positions.unsqueeze(1)  # [batch, 1, num_agvs, 2]
        
        agv_distances = torch.sum(torch.abs(pos_i - pos_j), dim=-1)  # [batch, num_agvs, num_agvs]
        
        # 分层距离权重
        near_mask = (agv_distances <= 3).float()
        mid_mask = ((agv_distances > 3) & (agv_distances <= 7)).float()
        far_mask = (agv_distances > 7).float()
        
        distance_constraints = (near_mask * ATTENTION_CONFIG.NEAR_WEIGHT +
                              mid_mask * ATTENTION_CONFIG.MID_WEIGHT +
                              far_mask * ATTENTION_CONFIG.FAR_WEIGHT)
        
        # 3. 协作需求约束 - C_cooperation^collab(i,j)
        # 简化实现：基于载重差异的协作需求
        cooperation_need = torch.abs(util_i - util_j)  # 载重差异越大，协作需求越高
        cooperation_constraints = ATTENTION_CONFIG.COOPERATION_WEIGHT * cooperation_need
        
        # 总协作约束
        total_constraints = balance_constraints + distance_constraints + cooperation_constraints
        
        # 对角线设为0（AGV不与自己协作）
        mask = torch.eye(num_agvs, device=agv_features.device).unsqueeze(0).expand(batch_size, -1, -1)
        total_constraints = total_constraints * (1 - mask)
        
        return total_constraints


class SingleLayerAGVTaskAGVAttention(nn.Module):
    """单层AGV-Task-AGV注意力机制
    
    实现研究方案的核心创新：
    1. 统一注意力架构：通过单一的异构注意力机制同时实现任务分配和AGV协作感知
    2. 双重注意力计算：任务分配注意力（AGV关注Task）+ 协作感知注意力（AGV关注AGV）
    3. 自适应融合：动态融合两种注意力输出
    4. Top-K稀疏化：降低计算复杂度
    """
    
    def __init__(self, feature_dim: int = ATTENTION_CONFIG.ATTENTION_DIM):
        super().__init__()
        self.feature_dim = feature_dim
        self.constraint_attention = ConstraintEnhancedAttention(feature_dim)
        
        # 注意力投影层
        self.query_proj = nn.Linear(feature_dim, feature_dim)
        self.key_proj = nn.Linear(feature_dim, feature_dim)
        self.value_proj = nn.Linear(feature_dim, feature_dim)
        
        # 自适应融合门控
        self.gate_proj = nn.Linear(feature_dim * 2, feature_dim)
        self.gate_activation = nn.Sigmoid()
        
        # 输出投影
        self.output_proj = nn.Linear(feature_dim, feature_dim)
        
        # Layer normalization
        self.layer_norm = nn.LayerNorm(feature_dim)
        
    def forward(self, agv_features: torch.Tensor, task_features: torch.Tensor,
                agv_positions: torch.Tensor, task_positions: torch.Tensor,
                agv_loads: torch.Tensor, task_weights: torch.Tensor,
                task_status: torch.Tensor) -> AttentionOutput:
        """前向传播
        
        Args:
            agv_features: AGV特征 [batch, num_agvs, feature_dim]
            task_features: 任务特征 [batch, num_tasks, feature_dim]
            agv_positions: AGV位置 [batch, num_agvs, 2]
            task_positions: 任务位置 [batch, num_tasks, 2]
            agv_loads: AGV当前载重 [batch, num_agvs]
            task_weights: 任务重量 [batch, num_tasks]
            task_status: 任务状态 [batch, num_tasks]
            
        Returns:
            注意力输出
        """
        batch_size, num_agvs, _ = agv_features.shape
        _, num_tasks, _ = task_features.shape
        
        # 1. 计算约束
        task_constraints = self.constraint_attention.calculate_task_constraints(
            agv_features, task_features, agv_positions, task_positions,
            agv_loads, task_weights, task_status
        )
        
        agv_constraints = self.constraint_attention.calculate_agv_constraints(
            agv_features, agv_positions, agv_loads
        )
        
        # 2. 任务分配注意力（AGV关注Task）
        task_attention_output, task_attention_weights = self._compute_task_attention(
            agv_features, task_features, task_constraints
        )
        
        # 3. 协作感知注意力（AGV关注AGV）
        agv_attention_output, agv_attention_weights = self._compute_agv_attention(
            agv_features, agv_constraints
        )
        
        # 4. 自适应融合
        enhanced_features = self._adaptive_fusion(
            agv_features, task_attention_output, agv_attention_output
        )
        
        # 5. 残差连接和层归一化
        enhanced_features = self.layer_norm(enhanced_features + agv_features)
        
        # 计算注意力评分
        attention_scores = self._calculate_attention_scores(
            task_attention_weights, agv_attention_weights
        )
        
        return AttentionOutput(
            enhanced_features=enhanced_features,
            task_attention_weights=task_attention_weights,
            agv_attention_weights=agv_attention_weights,
            attention_scores=attention_scores
        )
    
    def _compute_task_attention(self, agv_features: torch.Tensor, task_features: torch.Tensor,
                              constraints: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """计算任务分配注意力"""
        # Query: AGV特征, Key/Value: 任务特征
        queries = self.query_proj(agv_features)  # [batch, num_agvs, feature_dim]
        keys = self.key_proj(task_features)      # [batch, num_tasks, feature_dim]
        values = self.value_proj(task_features)  # [batch, num_tasks, feature_dim]
        
        # 计算注意力分数
        scores = torch.matmul(queries, keys.transpose(-2, -1)) / np.sqrt(self.feature_dim)
        scores = scores + constraints  # 添加约束
        
        # Top-K稀疏化
        if ATTENTION_CONFIG.TOP_K_TASKS < task_features.shape[1]:
            scores = self._apply_topk_sparsity(scores, ATTENTION_CONFIG.TOP_K_TASKS, dim=-1)
        
        # 计算注意力权重
        attention_weights = F.softmax(scores, dim=-1)
        
        # 计算输出
        output = torch.matmul(attention_weights, values)
        
        return output, attention_weights
    
    def _compute_agv_attention(self, agv_features: torch.Tensor,
                             constraints: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """计算协作感知注意力"""
        # Query/Key/Value: AGV特征
        queries = self.query_proj(agv_features)  # [batch, num_agvs, feature_dim]
        keys = self.key_proj(agv_features)       # [batch, num_agvs, feature_dim]
        values = self.value_proj(agv_features)   # [batch, num_agvs, feature_dim]
        
        # 计算注意力分数
        scores = torch.matmul(queries, keys.transpose(-2, -1)) / np.sqrt(self.feature_dim)
        scores = scores + constraints  # 添加约束
        
        # Top-K稀疏化
        if ATTENTION_CONFIG.TOP_K_AGVS < agv_features.shape[1]:
            scores = self._apply_topk_sparsity(scores, ATTENTION_CONFIG.TOP_K_AGVS, dim=-1)
        
        # 计算注意力权重
        attention_weights = F.softmax(scores, dim=-1)
        
        # 计算输出
        output = torch.matmul(attention_weights, values)
        
        return output, attention_weights
    
    def _adaptive_fusion(self, original_features: torch.Tensor,
                        task_output: torch.Tensor, agv_output: torch.Tensor) -> torch.Tensor:
        """自适应融合两种注意力输出"""
        # 拼接两种注意力输出
        combined = torch.cat([task_output, agv_output], dim=-1)
        
        # 计算融合门控
        gate = self.gate_activation(self.gate_proj(combined))
        
        # 自适应融合
        fused_output = gate * task_output + (1 - gate) * agv_output
        
        # 输出投影
        final_output = self.output_proj(fused_output)
        
        return final_output
    
    def _apply_topk_sparsity(self, scores: torch.Tensor, k: int, dim: int) -> torch.Tensor:
        """应用Top-K稀疏化"""
        # 获取Top-K索引
        topk_values, topk_indices = torch.topk(scores, k, dim=dim)
        
        # 创建稀疏掩码
        sparse_scores = torch.full_like(scores, -float('inf'))
        sparse_scores.scatter_(dim, topk_indices, topk_values)
        
        return sparse_scores
    
    def _calculate_attention_scores(self, task_weights: torch.Tensor,
                                  agv_weights: torch.Tensor) -> Dict[str, float]:
        """计算注意力评分指标"""
        # 任务注意力集中度
        task_entropy = -torch.sum(task_weights * torch.log(task_weights + 1e-8), dim=-1)
        task_concentration = 1.0 - task_entropy.mean().item() / np.log(task_weights.shape[-1])
        
        # AGV协作注意力集中度
        agv_entropy = -torch.sum(agv_weights * torch.log(agv_weights + 1e-8), dim=-1)
        agv_concentration = 1.0 - agv_entropy.mean().item() / np.log(agv_weights.shape[-1])
        
        return {
            'task_attention_concentration': task_concentration,
            'agv_attention_concentration': agv_concentration,
            'overall_attention_quality': (task_concentration + agv_concentration) / 2.0
        }
