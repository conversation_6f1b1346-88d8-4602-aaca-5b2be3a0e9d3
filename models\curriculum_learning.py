"""
渐进式课程学习管理器 - 基于单层AGV-Task-AGV注意力机制的MAPPO多AGV调度优化系统
实现研究方案第7章的六阶段渐进式课程学习
"""

import numpy as np
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
from utils.config import CURRICULUM_CONFIG, ENV_CONFIG


class CurriculumStage(Enum):
    """课程学习阶段"""
    STAGE1_BASIC_MOVEMENT = 1      # 基础移动学习
    STAGE2_SINGLE_MULTI_TASK = 2   # 单AGV多任务学习
    STAGE3_DUAL_COOPERATION = 3    # 双AGV协作学习
    STAGE4_MULTI_COOPERATION = 4   # 多AGV复杂协作
    STAGE5_FULL_OPTIMIZATION = 5   # 满载协作优化
    STAGE6_DYNAMIC_ADAPTATION = 6  # 动态环境适应


@dataclass
class StageConfig:
    """阶段配置"""
    stage: CurriculumStage
    episode_range: Tuple[int, int]  # episode范围
    num_agvs: int
    num_tasks: int
    task_weight_range: Tuple[int, int]  # 任务重量范围
    success_criteria: Dict[str, float]
    special_settings: Dict[str, Any]  # 特殊设置


@dataclass
class StageProgress:
    """阶段进度"""
    current_episode: int
    stage_episodes: int
    performance_history: List[Dict[str, float]]
    success_count: int
    total_evaluations: int
    is_completed: bool
    next_stage_ready: bool


class CurriculumLearningManager:
    """渐进式课程学习管理器
    
    实现研究方案中的六阶段学习设计：
    1. 阶段1：基础移动学习（Episode 0-500）
    2. 阶段2：单AGV多任务学习（Episode 500-1500）
    3. 阶段3：双AGV协作学习（Episode 1500-3000）
    4. 阶段4：多AGV复杂协作（Episode 3000-5000）
    5. 阶段5：满载协作优化（Episode 5000-7500）
    6. 阶段6：动态环境适应（Episode 7500-10000）
    """
    
    def __init__(self):
        """初始化课程学习管理器"""
        self.current_stage = CurriculumStage.STAGE1_BASIC_MOVEMENT
        self.stage_configs = self._initialize_stage_configs()
        self.stage_progress = {}
        self.global_episode = 0
        
        # 初始化各阶段进度
        for stage in CurriculumStage:
            self.stage_progress[stage] = StageProgress(
                current_episode=0,
                stage_episodes=0,
                performance_history=[],
                success_count=0,
                total_evaluations=0,
                is_completed=False,
                next_stage_ready=False
            )
    
    def _initialize_stage_configs(self) -> Dict[CurriculumStage, StageConfig]:
        """初始化阶段配置"""
        configs = {}
        
        # 阶段1：基础移动学习
        configs[CurriculumStage.STAGE1_BASIC_MOVEMENT] = StageConfig(
            stage=CurriculumStage.STAGE1_BASIC_MOVEMENT,
            episode_range=(0, CURRICULUM_CONFIG.STAGE1_EPISODES),
            num_agvs=CURRICULUM_CONFIG.STAGE1_NUM_AGVS,
            num_tasks=CURRICULUM_CONFIG.STAGE1_NUM_TASKS,
            task_weight_range=(CURRICULUM_CONFIG.STAGE1_TASK_WEIGHT, CURRICULUM_CONFIG.STAGE1_TASK_WEIGHT),
            success_criteria=CURRICULUM_CONFIG.STAGE1_SUCCESS_CRITERIA,
            special_settings={'simplified_map': True, 'no_obstacles': True}
        )
        
        # 阶段2：单AGV多任务学习
        configs[CurriculumStage.STAGE2_SINGLE_MULTI_TASK] = StageConfig(
            stage=CurriculumStage.STAGE2_SINGLE_MULTI_TASK,
            episode_range=(CURRICULUM_CONFIG.STAGE1_EPISODES, CURRICULUM_CONFIG.STAGE2_EPISODES),
            num_agvs=CURRICULUM_CONFIG.STAGE2_NUM_AGVS,
            num_tasks=CURRICULUM_CONFIG.STAGE2_NUM_TASKS,
            task_weight_range=(1, 10),
            success_criteria=CURRICULUM_CONFIG.STAGE2_SUCCESS_CRITERIA,
            special_settings={'load_constraints_active': True}
        )
        
        # 阶段3：双AGV协作学习
        configs[CurriculumStage.STAGE3_DUAL_COOPERATION] = StageConfig(
            stage=CurriculumStage.STAGE3_DUAL_COOPERATION,
            episode_range=(CURRICULUM_CONFIG.STAGE2_EPISODES, CURRICULUM_CONFIG.STAGE3_EPISODES),
            num_agvs=CURRICULUM_CONFIG.STAGE3_NUM_AGVS,
            num_tasks=CURRICULUM_CONFIG.STAGE3_NUM_TASKS,
            task_weight_range=(1, 12),
            success_criteria=CURRICULUM_CONFIG.STAGE3_SUCCESS_CRITERIA,
            special_settings={'cooperation_rewards_active': True}
        )
        
        # 阶段4：多AGV复杂协作
        configs[CurriculumStage.STAGE4_MULTI_COOPERATION] = StageConfig(
            stage=CurriculumStage.STAGE4_MULTI_COOPERATION,
            episode_range=(CURRICULUM_CONFIG.STAGE3_EPISODES, CURRICULUM_CONFIG.STAGE4_EPISODES),
            num_agvs=CURRICULUM_CONFIG.STAGE4_NUM_AGVS,
            num_tasks=CURRICULUM_CONFIG.STAGE4_NUM_TASKS,
            task_weight_range=(1, 15),
            success_criteria=CURRICULUM_CONFIG.STAGE4_SUCCESS_CRITERIA,
            special_settings={'full_attention_mechanism': True}
        )
        
        # 阶段5：满载协作优化
        configs[CurriculumStage.STAGE5_FULL_OPTIMIZATION] = StageConfig(
            stage=CurriculumStage.STAGE5_FULL_OPTIMIZATION,
            episode_range=(CURRICULUM_CONFIG.STAGE4_EPISODES, CURRICULUM_CONFIG.STAGE5_EPISODES),
            num_agvs=CURRICULUM_CONFIG.STAGE5_NUM_AGVS,
            num_tasks=CURRICULUM_CONFIG.STAGE5_NUM_TASKS,
            task_weight_range=(1, 15),
            success_criteria=CURRICULUM_CONFIG.STAGE5_SUCCESS_CRITERIA,
            special_settings={'full_environment': True}
        )
        
        # 阶段6：动态环境适应
        configs[CurriculumStage.STAGE6_DYNAMIC_ADAPTATION] = StageConfig(
            stage=CurriculumStage.STAGE6_DYNAMIC_ADAPTATION,
            episode_range=(CURRICULUM_CONFIG.STAGE5_EPISODES, CURRICULUM_CONFIG.STAGE6_EPISODES),
            num_agvs=ENV_CONFIG.NUM_AGVS,
            num_tasks=ENV_CONFIG.NUM_TASKS,
            task_weight_range=(1, 15),
            success_criteria=CURRICULUM_CONFIG.STAGE6_SUCCESS_CRITERIA,
            special_settings={'dynamic_tasks': True, 'dynamic_weights': True, 'environmental_disturbances': True}
        )
        
        return configs
    
    def get_current_stage_config(self) -> StageConfig:
        """获取当前阶段配置"""
        return self.stage_configs[self.current_stage]
    
    def update_episode(self, episode: int, performance_metrics: Dict[str, float]) -> bool:
        """更新episode并检查是否需要阶段转换
        
        Args:
            episode: 当前episode
            performance_metrics: 性能指标
            
        Returns:
            是否发生了阶段转换
        """
        self.global_episode = episode
        
        # 更新当前阶段进度
        progress = self.stage_progress[self.current_stage]
        progress.current_episode = episode
        progress.stage_episodes += 1
        progress.performance_history.append(performance_metrics)
        progress.total_evaluations += 1
        
        # 检查是否满足成功标准
        if self._check_success_criteria(performance_metrics):
            progress.success_count += 1
        
        # 检查是否需要阶段转换
        stage_changed = False
        if self._should_advance_stage():
            stage_changed = self._advance_to_next_stage()
        
        return stage_changed
    
    def _check_success_criteria(self, performance_metrics: Dict[str, float]) -> bool:
        """检查是否满足当前阶段的成功标准"""
        config = self.stage_configs[self.current_stage]
        success_criteria = config.success_criteria
        
        for metric_name, threshold in success_criteria.items():
            if metric_name not in performance_metrics:
                return False
            
            if performance_metrics[metric_name] < threshold:
                return False
        
        return True
    
    def _should_advance_stage(self) -> bool:
        """判断是否应该进入下一阶段"""
        progress = self.stage_progress[self.current_stage]
        config = self.stage_configs[self.current_stage]
        
        # 检查是否达到最小episode要求
        min_episodes = 100  # 每个阶段至少100个episode
        if progress.stage_episodes < min_episodes:
            return False
        
        # 检查是否达到episode上限
        if self.global_episode >= config.episode_range[1]:
            return True
        
        # 检查性能稳定性
        if len(progress.performance_history) >= 100:
            recent_performance = progress.performance_history[-100:]
            success_rate = progress.success_count / progress.total_evaluations
            
            # 连续100个episode的平均性能 >= 阶段目标
            if success_rate >= 0.8:  # 80%的成功率
                # 检查性能稳定性：标准差 <= 10%
                recent_success = [1 if self._check_success_criteria(perf) else 0 
                                for perf in recent_performance]
                stability = 1.0 - np.std(recent_success)
                
                if stability >= 0.9:  # 90%的稳定性
                    return True
        
        return False
    
    def _advance_to_next_stage(self) -> bool:
        """进入下一阶段"""
        current_stage_num = self.current_stage.value
        
        if current_stage_num >= 6:  # 已经是最后阶段
            return False
        
        # 标记当前阶段完成
        self.stage_progress[self.current_stage].is_completed = True
        
        # 进入下一阶段
        next_stage = CurriculumStage(current_stage_num + 1)
        self.current_stage = next_stage
        
        # 重置新阶段的进度
        self.stage_progress[next_stage] = StageProgress(
            current_episode=self.global_episode,
            stage_episodes=0,
            performance_history=[],
            success_count=0,
            total_evaluations=0,
            is_completed=False,
            next_stage_ready=False
        )
        
        print(f"课程学习：进入{next_stage.name}阶段 (Episode {self.global_episode})")
        
        return True
    
    def get_adaptive_learning_rate(self, base_lr: float) -> float:
        """获取自适应学习率"""
        progress = self.stage_progress[self.current_stage]
        
        if progress.total_evaluations == 0:
            return base_lr
        
        # 基于当前阶段和性能调整学习率
        stage_factor = 0.9 ** (self.current_stage.value - 1)  # 随阶段递减
        
        # 基于性能调整
        success_rate = progress.success_count / progress.total_evaluations
        performance_factor = 1.0 - success_rate  # 性能越好，学习率越低
        
        adjusted_lr = base_lr * stage_factor * (1.0 + performance_factor)
        
        return max(adjusted_lr, base_lr * 0.1)  # 最低为基础学习率的10%
    
    def get_adaptive_exploration_rate(self, base_epsilon: float) -> float:
        """获取自适应探索率"""
        progress = self.stage_progress[self.current_stage]
        
        # 基于阶段进度的探索率衰减
        stage_progress_ratio = min(1.0, progress.stage_episodes / 1000)  # 假设每阶段1000个episode
        exploration_decay = np.exp(-2.0 * stage_progress_ratio)
        
        adjusted_epsilon = base_epsilon * exploration_decay
        
        return max(adjusted_epsilon, 0.01)  # 最低1%的探索率
    
    def get_reward_weights_adjustment(self) -> Dict[str, float]:
        """获取奖励权重调整"""
        adjustments = {}
        
        if self.current_stage == CurriculumStage.STAGE1_BASIC_MOVEMENT:
            adjustments = {
                'completion_weight_multiplier': 2.0,  # 强化任务完成
                'movement_cost_multiplier': 0.5       # 减少移动惩罚
            }
        elif self.current_stage == CurriculumStage.STAGE2_SINGLE_MULTI_TASK:
            adjustments = {
                'load_efficiency_multiplier': 1.5,    # 强化载重效率
                'multi_task_multiplier': 2.0          # 强化多任务奖励
            }
        elif self.current_stage.value >= 3:  # 协作阶段
            adjustments = {
                'load_balance_multiplier': 1.5,       # 强化负载均衡
                'cooperation_multiplier': 2.0         # 强化协作奖励
            }
        
        return adjustments
    
    def get_curriculum_statistics(self) -> Dict[str, Any]:
        """获取课程学习统计信息"""
        stats = {
            'current_stage': self.current_stage.name,
            'global_episode': self.global_episode,
            'stage_progress': {}
        }
        
        for stage, progress in self.stage_progress.items():
            stats['stage_progress'][stage.name] = {
                'episodes_completed': progress.stage_episodes,
                'success_rate': progress.success_count / max(1, progress.total_evaluations),
                'is_completed': progress.is_completed,
                'average_performance': self._calculate_average_performance(progress.performance_history)
            }
        
        return stats
    
    def _calculate_average_performance(self, performance_history: List[Dict[str, float]]) -> Dict[str, float]:
        """计算平均性能"""
        if not performance_history:
            return {}
        
        # 获取所有指标名称
        all_metrics = set()
        for perf in performance_history:
            all_metrics.update(perf.keys())
        
        # 计算每个指标的平均值
        avg_performance = {}
        for metric in all_metrics:
            values = [perf.get(metric, 0.0) for perf in performance_history]
            avg_performance[metric] = np.mean(values)
        
        return avg_performance
    
    def should_use_simplified_environment(self) -> bool:
        """是否应该使用简化环境"""
        config = self.stage_configs[self.current_stage]
        return config.special_settings.get('simplified_map', False)
    
    def should_enable_dynamic_tasks(self) -> bool:
        """是否应该启用动态任务"""
        config = self.stage_configs[self.current_stage]
        return config.special_settings.get('dynamic_tasks', False)
    
    def get_task_weight_range(self) -> Tuple[int, int]:
        """获取当前阶段的任务重量范围"""
        config = self.stage_configs[self.current_stage]
        return config.task_weight_range
