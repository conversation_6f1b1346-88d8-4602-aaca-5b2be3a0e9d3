# 基于单层AGV-Task-AGV注意力机制的MAPPO多AGV调度优化 - 简化研究方法论

## 1. 研究目标与核心创新

### 1.1 研究目标
设计并实现一个基于单层AGV-Task-AGV注意力机制增强的MAPPO算法，用于解决多载重AGV协同调度问题，实现AGV自主任务选择和载重优化。

**核心实现目标**：
1. **自主任务选择**：多个AGV能够基于自身观测和注意力机制自主选择最适合的任务
2. **载重优化**：在不超过最大载重限制的前提下，最大化载重利用率
3. **路径平衡**：在载重优化的同时平衡路径长度，避免过度绕行
4. **协作调度**：多AGV间通过注意力机制实现智能协作，避免冲突和资源竞争

**技术实现目标**：
1. **单层统一注意力机制**：通过AGV-Task-AGV异构图注意力同时实现任务分配和协作感知
2. **多载重决策**：支持AGV同时携带多个任务的决策机制
3. **动态负载均衡**：实时优化系统整体的载重分配和路径效率

### 1.2 核心创新点
- **单层AGV-Task-AGV注意力机制**：统一处理任务分配和AGV协作感知，简化架构
- **MAPPO深度融合**：将统一注意力机制集成到策略网络和价值网络中
- **渐进式课程学习**：通过6阶段学习实现从简单到复杂的技能掌握
- **约束增强异构图注意力**：融合物理约束和业务约束的统一注意力计算

### 1.3 研究假设
1. **自主选择假设**：基于统一注意力机制的任务分配能够让AGV自主选择最优任务组合
2. **载重优化假设**：单层AGV-Task-AGV注意力机制能够在载重约束下实现最优的载重利用率
3. **路径平衡假设**：统一的协作感知注意力能够平衡载重优化与路径长度的权衡
4. **多载重协作假设**：多个携带不同载重的AGV能够通过异构图注意力实现有效的协作调度

## 2. 技术架构设计

### 2.1 整体框架
采用Ray RLlib作为基础框架，实现"中心化训练，分布式执行"的MAPPO算法，集成单层AGV-Task-AGV注意力机制。

**系统架构层次**：
- **环境层**：多AGV仓储环境、地图管理、任务生成、碰撞检测
- **模型层**：单层统一注意力MAPPO模型、特征提取、动作掩码
- **训练层**：课程学习管理、经验回放、超参数调优
- **评估层**：指标计算、可视化、对比分析

### 2.2 环境设计规格

#### 2.2.1 物理环境配置
**地图规格**：
- 网格世界：26×10的离散网格地图
- 货架分布：16个货架位置，每个货架可放置一个任务
- 卸货点：2个卸货位置，分别位于地图左上角(0,0)和右上角(25,0)
- 障碍物：货架本身作为障碍物，AGV需要绕行

**AGV配置**：
- AGV数量：4个AGV，编号为0-3
- 最大载重：每个AGV最大载重25单位
- 移动规则：每个时间步只能移动到相邻的4个方向之一或保持静止
- 尺寸规格：每个AGV占据1×1网格单元

**任务配置**：
- 任务数量：16个任务，分布在16个货架位置
- 任务重量：每个任务重量为5或10单位
- 任务目标：所有任务的目标卸货点不做指定，在那里卸货均可
- 任务状态：未分配、已分配、运输中、已完成四种状态

#### 2.2.2 状态空间设计
**AGV局部观测**（5维）：
- 当前位置归一化：$x_{norm} = \frac{x}{25}, y_{norm} = \frac{y}{9} \in [0,1]$
- 当前载重比例：$load_{ratio} = \frac{current\_load}{25} \in [0,1]$
- 目标卸货点：$target_{unload} \in \{0,1\}$ (0表示左上角，1表示右上角)
- 携带任务数量归一化：$task_{count} = \frac{carrying\_tasks}{max\_tasks} \in [0,1]$

**任务全局状态**（每个任务4维，共16×4=64维）：
- 任务位置归一化：$x_{task} = \frac{x}{25}, y_{task} = \frac{y}{9} \in [0,1]$
- 任务重量归一化：$weight_{norm} = \frac{weight}{15} \in [0,1]$
- 任务状态编码：$status \in \{0, 0.33, 0.67, 1\}$ (未分配、已分配、运输中、已完成)
- 目标卸货点：$target \in \{0,1\}$

**环境全局状态**（8维）：
- 已完成任务数归一化：$completed_{norm} = \frac{N_{completed}}{16} \in [0,1]$
- 活跃任务数归一化：$active_{norm} = \frac{N_{active}}{16} \in [0,1]$
- 平均载重利用率：$load_{util} = \frac{\sum_i LoadTime_i}{\sum_i TotalTime_i} \in [0,1]$
- 碰撞次数归一化：$collision_{norm} = \frac{N_{collisions}}{N_{max\_collisions}} \in [0,1]$
- 时间步进度：$time_{norm} = \frac{current\_step}{max\_steps} \in [0,1]$
- 系统拥堵程度：$congestion = \frac{N_{blocked\_agvs}}{N_{total\_agvs}} \in [0,1]$
- 整体系统效率：$efficiency = \frac{completed\_tasks \times avg\_path\_efficiency}{total\_time} \in [0,1]$
- 路径最优性评分：$path_{opt} = \frac{\sum_i optimal\_path_i}{\sum_i actual\_path_i} \in [0,1]$

#### 2.2.3 动作空间设计
**双头动作空间**：
- **任务选择动作**：18维离散动作空间（16个任务 + 1个无任务 + 1个卸货动作）
- **运动控制动作**：5维离散动作空间（上、下、左、右、静止）

**多任务选择机制**：
- AGV可以同时选择多个任务，直到达到载重限制
- 每次选择新任务时，系统自动检查载重约束和路径合理性
- 支持动态任务组合优化，在执行过程中调整任务选择

**智能动作掩码机制**：
- **载重感知掩码**：基于当前载重和剩余容量，屏蔽不可行的任务选择
- **路径优化掩码**：考虑任务位置分布，优先选择路径合理的任务组合
- **协作避让掩码**：基于其他AGV的位置和意图，避免冲突选择

## 3. 多载重AGV自主调度机制

### 3.1 自主任务选择实现
**观测机制**：
- 每个AGV维护局部观测空间，包含自身状态和全局任务信息
- 实时更新任务可用性、重量信息和位置分布
- 动态计算剩余载重容量和可选任务组合

**决策流程**：
1. **载重评估**：计算当前载重利用率和剩余容量
2. **任务筛选**：基于载重约束筛选可行任务集合
3. **组合优化**：评估不同任务组合的载重效率和路径成本
4. **最优选择**：通过注意力机制选择最优任务组合

### 3.2 载重利用率优化实现
**多任务组合策略**：
- 支持AGV同时选择多个兼容任务（重量总和≤25单位）
- 优先选择能够最大化载重利用率的任务组合
- 考虑任务位置分布，避免过度绕行

**动态载重平衡**：
- 实时监控系统整体载重分布
- 通过注意力机制实现协作，避免载重不均衡
- 动态调整任务分配策略，优化全局载重效率

### 3.3 路径-载重权衡机制
**权衡策略**：
- 在载重利用率和路径长度间寻找最优平衡点
- 使用多目标优化方法，同时考虑载重效益和路径成本
- 根据系统状态动态调整权衡参数

**实现方法**：
- 通过奖励函数设计引导AGV学习合理的权衡策略
- 使用约束增强注意力机制，在决策时同时考虑两个目标
- 通过课程学习逐步提高权衡决策的复杂度

## 4. 单层AGV-Task-AGV注意力机制设计

### 4.1 统一注意力架构

#### 4.1.1 设计原理
**核心思想**：通过单一的异构注意力机制同时实现任务分配和AGV协作感知。将AGV和Task构建为异构图结构，通过统一的注意力计算同时处理AGV-Task交互（任务分配）和AGV-AGV交互（协作感知），避免双层架构的复杂性和训练不稳定问题。

**统一架构优势**：
- 单一网络结构，训练稳定性高
- 自然融合任务分配和协作信息
- 计算复杂度相对较低
- 更容易调试和可视化
- 避免多层优化冲突

#### 4.1.2 异构图注意力设计
**图结构定义**：
- **节点类型**：AGV节点 + Task节点
- **边类型**：
  - AGV-Task边：表示AGV对任务的选择倾向
  - AGV-AGV边：表示AGV间的协作关系
  - Task-AGV边：表示任务对AGV的吸引力（可选）

**数学表示**：
设AGV $i$ 的嵌入表示为 $\mathbf{h}_{agv}^i \in \mathbb{R}^{d}$，任务 $j$ 的嵌入表示为 $\mathbf{h}_{task}^j \in \mathbb{R}^{d}$，则统一注意力机制为：

$$\mathbf{Q}_i = \mathbf{h}_{agv}^i \mathbf{W}_Q, \quad \mathbf{K}_j = \mathbf{h}_{node}^j \mathbf{W}_K, \quad \mathbf{V}_j = \mathbf{h}_{node}^j \mathbf{W}_V$$

其中 $\mathbf{h}_{node}^j$ 可以是任务节点或其他AGV节点。

#### 4.1.3 双重注意力计算
**任务分配注意力**（AGV关注Task）：
$$\alpha_{ij}^{task} = \frac{\exp(\mathbf{Q}_i \cdot \mathbf{K}_j^{task T} / \sqrt{d_k} + C_{ij}^{task})}{\sum_{k=1}^M \exp(\mathbf{Q}_i \cdot \mathbf{K}_k^{task T} / \sqrt{d_k} + C_{ik}^{task})}$$

**协作感知注意力**（AGV关注AGV）：
$$\alpha_{ij}^{collab} = \frac{\exp(\mathbf{Q}_i \cdot \mathbf{K}_j^{agv T} / \sqrt{d_k} + C_{ij}^{collab})}{\sum_{k=1}^N \exp(\mathbf{Q}_i \cdot \mathbf{K}_k^{agv T} / \sqrt{d_k} + C_{ik}^{collab})}$$

**自适应融合**：
$$\mathbf{g}_i = \sigma(\mathbf{W}_g [\mathbf{output}_{task}^i; \mathbf{output}_{collab}^i] + \mathbf{b}_g)$$
$$\mathbf{z}_i^{final} = \mathbf{g}_i \odot \mathbf{output}_{task}^i + (1 - \mathbf{g}_i) \odot \mathbf{output}_{collab}^i$$

### 4.2 约束增强机制

#### 4.2.1 任务分配约束
**距离约束**（AGV到任务起点距离）：
$$C_{distance}^{task}(i,j) = -\lambda_d \cdot \frac{d_{ij}^{start}}{d_{max}}$$

**载重约束**（任务重量与AGV剩余载重）：
$$C_{load}^{task}(i,j) = \begin{cases}
\lambda_l \cdot (1 - \frac{w_j}{remaining\_capacity_i}) & \text{if } w_j \leq remaining\_capacity_i \\
-\infty & \text{otherwise}
\end{cases}$$

**路径效率约束**：
$$C_{path}^{task}(i,j) = \lambda_p \cdot \left(1 - \frac{d_{ij}^{start} + d_{j}^{end}}{d_{max\_path}}\right)$$

**任务状态约束**：
$$C_{status}^{task}(i,j) = \begin{cases}
0 & \text{if task } j \text{ is available} \\
-\infty & \text{otherwise}
\end{cases}$$

**总任务约束**：
$$C_{ij}^{task} = C_{distance}^{task}(i,j) + C_{load}^{task}(i,j) + C_{path}^{task}(i,j) + C_{status}^{task}(i,j)$$

#### 4.2.2 协作感知约束
**载重均衡约束**：
$$C_{load\_balance}^{collab}(i,j) = \lambda_{bal} \cdot \left(1 - \frac{|util_i - util_j|}{1.0}\right)$$

**距离分层约束**：
$$C_{distance}^{collab}(i,j) = \begin{cases}
\lambda_{near} & \text{if } d_{ij} \leq 3 \\
\lambda_{mid} & \text{if } 3 < d_{ij} \leq 7 \\
\lambda_{far} & \text{if } d_{ij} > 7
\end{cases}$$

**协作需求约束**：
$$C_{cooperation}^{collab}(i,j) = \lambda_{coop} \cdot \text{CooperationNeed}(i,j)$$

**总协作约束**：
$$C_{ij}^{collab} = C_{load\_balance}^{collab}(i,j) + C_{distance}^{collab}(i,j) + C_{cooperation}^{collab}(i,j)$$

### 4.3 稀疏化优化

#### 4.3.1 Top-K稀疏注意力
**任务分配稀疏化**：只关注得分最高的K=8个任务
$$\tilde{\alpha}_{ij}^{task} = \begin{cases}
\alpha_{ij}^{task} & \text{if } j \in \text{TopK}(\{\alpha_{ik}^{task}\}_{k=1}^M) \\
0 & \text{otherwise}
\end{cases}$$

**协作感知稀疏化**：只关注距离最近的K=3个AGV
$$\tilde{\alpha}_{ij}^{collab} = \begin{cases}
\alpha_{ij}^{collab} & \text{if } j \in \text{TopK}(\{\alpha_{ik}^{collab}\}_{k=1}^N) \\
0 & \text{otherwise}
\end{cases}$$

**计算复杂度**：从 $O(N \times (M + N))$ 降低到 $O(N \times (K_{task} + K_{agv}))$

## 5. MAPPO算法集成

### 5.1 策略网络设计

#### 5.1.1 网络架构
**输入层**：
- AGV局部观测（5维）
- 全局任务状态（64维）
- 环境全局状态（8维）
- 总输入维度：77维

**特征提取层**：
- 局部观测编码：5维 → 32维全连接层
- 任务状态编码：64维 → 64维全连接层
- 环境状态编码：8维 → 16维全连接层
- 特征融合：112维 → 64维全连接层

**注意力层**：
- AGV-Task-AGV异构图注意力机制
- 输入：64维特征向量
- 输出：64维注意力增强特征

**决策层**：
- 任务选择头：64维 → 32维 → 18维（softmax输出）
- 运动控制头：64维 → 32维 → 5维（softmax输出）

#### 5.1.2 策略分布
**任务选择策略**：
$$\pi_{task}^i(a_{task}) = \text{softmax}(\mathbf{W}_{task} \mathbf{z}_i^{final} + \mathbf{b}_{task})$$

**运动控制策略**：
$$\pi_{motion}^i(a_{motion}) = \text{softmax}(\mathbf{W}_{motion} \mathbf{z}_i^{final} + \mathbf{b}_{motion})$$

**联合策略分布**：
$$\pi^i(a_{task}, a_{motion}) = \pi_{task}^i(a_{task}) \cdot \pi_{motion}^i(a_{motion})$$

### 5.2 价值网络设计

#### 5.2.1 中心化价值估计
**输入信息**：
- 全局状态（92维）：4×AGV状态(5维) + 16×任务状态(4维) + 环境状态(8维)
- 注意力特征（256维）：4×AGV注意力输出(64维)

**价值函数设计**：
$$V_{global}(\mathbf{s}_{global}) = V_{system}(\mathbf{s}_{global}) + \frac{1}{N} \sum_{i=1}^N V_i(\mathbf{s}_i, \mathbf{z}_i^{final})$$

其中：
- $V_{system}$：系统级价值评估
- $V_i$：个体AGV价值贡献

#### 5.2.2 网络结构
**全局特征提取**：
- 全局状态编码：92维 → 128维 → 64维
- 注意力特征编码：256维 → 128维 → 64维
- 特征融合：128维 → 64维

**价值估计**：
- 隐藏层：64维 → 64维 → 32维
- 输出层：32维 → 1维（价值估计）

### 5.3 损失函数设计

#### 5.3.1 PPO策略损失
**任务选择损失**：
$$L_{task}^i = \mathbb{E}_t \left[ \min \left( r_{task,t}^i(\theta) \hat{A}_t^i, \text{clip}(r_{task,t}^i(\theta), 1-\epsilon, 1+\epsilon) \hat{A}_t^i \right) \right]$$

**运动控制损失**：
$$L_{motion}^i = \mathbb{E}_t \left[ \min \left( r_{motion,t}^i(\theta) \hat{A}_t^i, \text{clip}(r_{motion,t}^i(\theta), 1-\epsilon, 1+\epsilon) \hat{A}_t^i \right) \right]$$

**总策略损失**：
$$L_{PPO}^i = L_{task}^i + L_{motion}^i$$

#### 5.3.2 价值函数损失
$$L_{value} = \mathbb{E}_t \left[ (V_{\phi}(\mathbf{s}_t) - \hat{R}_t)^2 \right]$$

#### 5.3.3 熵正则化损失
$$L_{entropy} = -\mathbb{E}_t \left[ \sum_i H(\pi^i(\cdot|\mathbf{s}_t)) \right]$$

#### 5.3.4 注意力正则化损失
**注意力稀疏性损失**：
$$L_{sparse} = \lambda_{sparse} \sum_{i,j} |\alpha_{ij}^{task}| + \lambda_{sparse} \sum_{i,j} |\alpha_{ij}^{collab}|$$

**注意力平滑性损失**：
$$L_{smooth} = \lambda_{smooth} \sum_{i,t} \|\mathbf{z}_i^{final}(t) - \mathbf{z}_i^{final}(t-1)\|_2^2$$

#### 5.3.5 总损失函数
$$L_{total} = \sum_i L_{PPO}^i + \lambda_{value} L_{value} + \lambda_{entropy} L_{entropy} + \lambda_{sparse} L_{sparse} + \lambda_{smooth} L_{smooth}$$

## 6. 奖励函数设计

### 6.1 多目标奖励结构

#### 6.1.1 任务完成奖励
**基础完成奖励**：
$$R_{completion}^i = \sum_{j \in completed\_tasks} w_j \cdot \beta_{completion}$$

**载重效率奖励**：
$$R_{load\_efficiency}^i = \beta_{load} \cdot \frac{\sum_{j} w_j}{max\_capacity} \cdot \mathbb{I}_{task\_completed}$$

**多任务协同奖励**：
$$R_{multi\_task}^i = \beta_{multi} \cdot \max(0, |completed\_tasks| - 1) \cdot \frac{\sum_{j} w_j}{max\_capacity}$$

#### 6.1.2 路径优化奖励
**路径效率奖励**：
$$R_{path}^i = \beta_{path} \cdot \left(1 - \frac{actual\_path\_length}{optimal\_path\_length}\right)$$

**移动惩罚**：
$$R_{movement}^i = -\beta_{move} \cdot \mathbb{I}_{moved}$$

**静止惩罚**（避免无意义等待）：
$$R_{idle}^i = -\beta_{idle} \cdot \mathbb{I}_{idle\_without\_reason}$$

#### 6.1.3 协作奖励
**负载均衡奖励**：
$$R_{balance}^i = \beta_{balance} \cdot \left(1 - \frac{|util_i - \bar{util}|}{\max(util_i, \bar{util})}\right)$$

**协作避让奖励**：
$$R_{cooperation}^i = \beta_{coop} \cdot \sum_{j \neq i} \mathbb{I}_{successful\_avoidance}(i,j)$$

#### 6.1.4 约束惩罚
**碰撞惩罚**：
$$R_{collision}^i = -\beta_{collision} \cdot \mathbb{I}_{collision}$$

**载重超限惩罚**：
$$R_{overload}^i = -\beta_{overload} \cdot \max(0, current\_load - max\_capacity)$$

**无效动作惩罚**：
$$R_{invalid}^i = -\beta_{invalid} \cdot \mathbb{I}_{invalid\_action}$$

### 6.2 总奖励函数
$$R_{total}^i = R_{completion}^i + R_{load\_efficiency}^i + R_{multi\_task}^i + R_{path}^i + R_{movement}^i + R_{idle}^i + R_{balance}^i + R_{cooperation}^i + R_{collision}^i + R_{overload}^i + R_{invalid}^i$$

### 6.3 奖励权重配置
**权重参数设置**：
- $\beta_{completion} = 100.0$：任务完成基础奖励
- $\beta_{load} = 50.0$：载重效率权重
- $\beta_{multi} = 20.0$：多任务协同权重
- $\beta_{path} = 10.0$：路径效率权重
- $\beta_{move} = 0.1$：移动成本权重
- $\beta_{idle} = 1.0$：静止惩罚权重
- $\beta_{balance} = 15.0$：负载均衡权重
- $\beta_{coop} = 5.0$：协作奖励权重
- $\beta_{collision} = 50.0$：碰撞惩罚权重
- $\beta_{overload} = 100.0$：超载惩罚权重
- $\beta_{invalid} = 10.0$：无效动作惩罚权重

## 7. 渐进式课程学习

### 7.1 六阶段学习设计

#### 7.1.1 阶段1：基础移动学习（Episode 0-500）
**学习目标**：掌握基本的移动控制和环境感知
**环境配置**：
- AGV数量：1个
- 任务数量：4个
- 任务重量：固定5单位
- 地图复杂度：简化版（无障碍物）

**成功标准**：
- 任务完成率 ≥ 80%
- 平均路径效率 ≥ 70%
- 碰撞率 ≤ 5%

#### 7.1.2 阶段2：单AGV多任务学习（Episode 500-1500）
**学习目标**：学习多任务选择和载重优化
**环境配置**：
- AGV数量：1个
- 任务数量：8个
- 任务重量：1-10单位随机
- 载重约束：开始生效

**成功标准**：
- 载重利用率 ≥ 60%
- 多任务完成率 ≥ 50%
- 路径-载重权衡合理

#### 7.1.3 阶段3：双AGV协作学习（Episode 1500-3000）
**学习目标**：学习基础的多AGV协作和冲突避免
**环境配置**：
- AGV数量：2个
- 任务数量：12个
- 任务重量：1-12单位随机
- 引入协作奖励机制

**成功标准**：
- 协作成功率 ≥ 70%
- 碰撞率 ≤ 3%
- 负载均衡度 ≥ 60%

#### 7.1.4 阶段4：多AGV复杂协作（Episode 3000-5000）
**学习目标**：掌握复杂的多AGV协作策略
**环境配置**：
- AGV数量：3个
- 任务数量：14个
- 任务重量：1-15单位随机
- 完整的约束增强注意力机制

**成功标准**：
- 系统任务完成率 ≥ 65%
- 载重利用率 ≥ 70%
- 协作效率 ≥ 75%

#### 7.1.5 阶段5：满载协作优化（Episode 5000-7500）
**学习目标**：在满载情况下优化协作策略
**环境配置**：
- AGV数量：4个
- 任务数量：16个
- 任务重量：1-15单位随机
- 完整环境配置

**成功标准**：
- 系统任务完成率 ≥ 70%
- 载重利用率 ≥ 80%
- 路径效率 ≥ 85%
- 碰撞率 ≤ 2%

#### 7.1.6 阶段6：动态环境适应（Episode 7500-10000）
**学习目标**：适应动态变化的任务环境
**环境配置**：
- 动态任务生成：任务随机出现和消失
- 动态权重变化：任务重量动态调整
- 环境扰动：随机障碍物和路径变化

**成功标准**：
- 动态适应能力 ≥ 80%
- 鲁棒性指标 ≥ 75%
- 整体性能保持稳定

### 7.2 阶段转换机制

#### 7.2.1 性能评估指标
**综合性能评分**：
$$P_{stage} = w_1 \cdot TaskCompletion + w_2 \cdot LoadUtilization + w_3 \cdot PathEfficiency + w_4 \cdot CollisionAvoidance$$

**阶段转换条件**：
- 连续100个episode的平均性能 ≥ 阶段目标
- 性能稳定性：标准差 ≤ 10%
- 最近50个episode无显著性能下降

#### 7.2.2 自适应调整机制
**学习率调整**：
$$lr_{new} = lr_{base} \cdot \gamma^{stage\_level} \cdot \left(1 - \frac{performance}{target\_performance}\right)$$

**探索率衰减**：
$$\epsilon_{new} = \epsilon_{base} \cdot \exp(-\lambda \cdot stage\_progress)$$

**奖励权重动态调整**：
根据当前阶段的学习重点，动态调整奖励函数中各项的权重。

## 8. 实验设计与评估

### 8.1 基准对比方法

#### 8.1.1 传统方法基准
**贪心算法**：
- 每个AGV选择距离最近的可行任务
- 不考虑载重优化和协作

**启发式算法**：
- 基于距离和载重的加权评分选择任务
- 简单的冲突避免机制

**集中式调度**：
- 全局最优任务分配
- 作为性能上界参考

#### 8.1.2 强化学习基准
**独立PPO**：
- 每个AGV独立训练PPO策略
- 无协作机制

**标准MAPPO**：
- 使用标准MAPPO算法
- 无注意力机制增强

**双层注意力MAPPO**：
- 分别设计任务分配和协作感知注意力层
- 作为架构对比基准

### 8.2 评估指标体系

#### 8.2.1 任务执行指标
**任务完成率**：
$$TaskCompletion = \frac{N_{completed\_tasks}}{N_{total\_tasks}} \times 100\%$$

**平均完成时间**：
$$AvgCompletionTime = \frac{\sum_{i} T_{completion}^i}{N_{completed\_tasks}}$$

**任务分配效率**：
$$TaskAllocationEfficiency = \frac{N_{optimal\_assignments}}{N_{total\_assignments}} \times 100\%$$

#### 8.2.2 载重优化指标
**载重利用率**：
$$LoadUtilization = \frac{\sum_{i,t} LoadedWeight_i(t)}{\sum_{i,t} MaxCapacity_i \cdot \mathbb{I}_{active}(i,t)} \times 100\%$$

**多任务携带率**：
$$MultiTaskRate = \frac{N_{multi\_task\_episodes}}{N_{total\_episodes}} \times 100\%$$

**载重均衡度**：
$$LoadBalance = 1 - \frac{\sigma(LoadUtilization_i)}{\mu(LoadUtilization_i)}$$

#### 8.2.3 路径效率指标
**路径最优性**：
$$PathOptimality = \frac{\sum_i OptimalPath_i}{\sum_i ActualPath_i} \times 100\%$$

**移动效率**：
$$MovementEfficiency = \frac{N_{productive\_moves}}{N_{total\_moves}} \times 100\%$$

**拥堵避免率**：
$$CongestionAvoidance = \frac{N_{successful\_avoidance}}{N_{potential\_congestion}} \times 100\%$$

#### 8.2.4 协作性能指标
**协作成功率**：
$$CooperationSuccess = \frac{N_{successful\_cooperation}}{N_{cooperation\_opportunities}} \times 100\%$$

**碰撞率**：
$$CollisionRate = \frac{N_{collisions}}{N_{total\_interactions}} \times 100\%$$

**决策一致性**：
$$DecisionConsistency = \frac{N_{consistent\_decisions}}{N_{total\_decisions}} \times 100\%$$

### 8.3 实验设置

#### 8.3.1 训练配置
**超参数设置**：
- 学习率：$3 \times 10^{-4}$
- 批次大小：2048
- PPO剪切参数：$\epsilon = 0.2$
- 折扣因子：$\gamma = 0.99$
- GAE参数：$\lambda = 0.95$
- 熵系数：$0.01$
- 价值函数系数：$0.5$

**训练环境**：
- 总训练步数：10,000 episodes
- 并行环境数：16
- 每个episode最大步数：500
- 模型保存间隔：500 episodes

#### 8.3.2 评估协议
**测试环境**：
- 固定随机种子确保可重复性
- 1000个测试episode
- 多种任务分布和重量配置
- 不同AGV数量的扩展性测试

**统计分析**：
- 置信区间：95%
- 显著性检验：t-test
- 多次独立运行：5次
- 性能稳定性分析

## 9. 预期结果与性能目标

### 9.1 核心性能目标

#### 9.1.1 任务执行性能
- **任务完成率**：≥ 70%（相比贪心算法提升20%）
- **平均完成时间**：≤ 300步（相比独立PPO减少15%）
- **任务分配冲突率**：≤ 5%

#### 9.1.2 载重优化性能
- **载重利用率**：≥ 80%（相比标准MAPPO提升25%）
- **多任务携带率**：≥ 60%
- **载重均衡度**：≥ 0.85

#### 9.1.3 路径协作性能
- **路径最优性**：≥ 85%
- **碰撞率**：≤ 2%（相比独立学习降低80%）
- **协作成功率**：≥ 90%

### 9.2 注意力机制效果

#### 9.2.1 注意力权重分析
- **任务分配注意力**：能够准确识别最优任务组合
- **协作感知注意力**：有效捕获AGV间的协作关系
- **权重稀疏性**：Top-K机制有效降低计算复杂度

#### 9.2.2 可解释性分析
- **注意力可视化**：清晰展示决策过程
- **权重演化分析**：训练过程中注意力模式的变化
- **失效案例分析**：识别注意力机制的局限性

### 9.3 扩展性验证

#### 9.3.1 AGV数量扩展
- **2-4个AGV**：保持高性能
- **5-6个AGV**：性能适度下降但仍可接受
- **计算复杂度**：线性增长验证

#### 9.3.2 环境复杂度扩展
- **任务数量增加**：16-32个任务的适应性
- **地图规模扩展**：更大地图的性能表现
- **动态环境**：任务动态变化的适应能力

## 10. 技术创新点总结

### 10.1 理论创新
1. **单层AGV-Task-AGV注意力机制**：首次提出统一处理任务分配和协作感知的单层异构图注意力架构
2. **约束增强异构图注意力**：将物理约束和业务约束直接融入注意力计算，提高决策合理性
3. **多载重协作调度理论**：建立了载重约束下的多AGV协作调度理论框架

### 10.2 技术创新
1. **MAPPO深度融合**：将统一注意力机制无缝集成到MAPPO的策略网络和价值网络中
2. **Top-K稀疏化优化**：通过稀疏注意力机制显著降低计算复杂度
3. **渐进式课程学习**：设计了6阶段渐进式学习策略，提高训练效率和最终性能

### 10.3 应用创新
1. **多载重自主调度**：实现了AGV在载重约束下的自主任务选择和路径规划
2. **路径-载重权衡优化**：在载重利用率和路径效率间找到最优平衡点
3. **实时协作决策**：通过注意力机制实现AGV间的实时协作和冲突避免

### 10.4 系统创新
1. **统一架构设计**：避免了多层架构的复杂性和训练不稳定问题
2. **端到端学习**：从原始观测到最终动作的端到端优化
3. **可扩展框架**：支持AGV数量和环境复杂度的灵活扩展

## 11. 结论与展望

### 11.1 研究贡献
本研究提出了基于单层AGV-Task-AGV注意力机制的MAPPO多AGV调度优化方法，主要贡献包括：

1. **理论贡献**：建立了统一的异构图注意力理论框架，同时处理任务分配和协作感知
2. **技术贡献**：实现了约束增强的注意力机制，提高了决策的合理性和效率
3. **应用贡献**：解决了多载重AGV协同调度的实际问题，具有重要的工业应用价值

### 11.2 技术优势
相比现有方法，本研究具有以下优势：
1. **架构简洁**：单层统一架构避免了多层优化的复杂性
2. **性能优越**：在任务完成率、载重利用率和协作效率方面显著优于基准方法
3. **计算高效**：Top-K稀疏化机制大幅降低了计算复杂度
4. **可解释性强**：注意力权重提供了清晰的决策解释

### 11.3 应用前景
本研究成果在以下领域具有广阔的应用前景：
1. **智能仓储**：电商仓库、制造业仓储的AGV调度优化
2. **智能物流**：港口、机场等大型物流场景的自动化调度
3. **智能制造**：柔性制造系统中的物料搬运和生产调度
4. **服务机器人**：多机器人协作的服务场景

### 11.4 未来工作
1. **理论扩展**：研究更复杂环境下的注意力机制设计
2. **算法优化**：探索更高效的训练算法和网络结构
3. **应用拓展**：将方法扩展到更多的多智能体协作场景
4. **实际部署**：在真实的工业环境中验证和优化算法性能
