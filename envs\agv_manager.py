"""
AGV管理器 - 多载重AGV通信增强调度系统
负责管理4个AGV的状态、行为和协作
"""

import numpy as np
from typing import List, Tuple, Dict, Optional, Set
from enum import Enum
from utils.config import ENV_CONFIG, ACTION_CONFIG
from envs.map_manager import MapManager
from envs.task_manager import TaskManager, Task


class AGVStatus(Enum):
    """AGV状态枚举"""
    IDLE = 0        # 空闲
    MOVING = 1      # 移动中
    LOADING = 2     # 装载中
    UNLOADING = 3   # 卸载中


class AGV:
    """AGV类"""
    
    def __init__(self, agv_id: int, initial_position: Tuple[int, int]):
        """初始化AGV
        
        Args:
            agv_id: AGV ID
            initial_position: 初始位置
        """
        self.id = agv_id
        self.position = initial_position
        self.max_capacity = ENV_CONFIG.MAX_CAPACITY
        self.current_load = 0
        self.status = AGVStatus.IDLE
        
        # 任务相关
        self.carrying_tasks: List[int] = []  # 携带的任务ID列表
        self.target_task_id: Optional[int] = None  # 目标任务ID
        self.target_position: Optional[Tuple[int, int]] = None  # 目标位置
        
        # 统计信息
        self.total_distance = 0
        self.idle_time = 0
        self.load_time = 0
        self.total_tasks_completed = 0
        
        # 历史轨迹
        self.position_history: List[Tuple[int, int]] = [initial_position]
    
    def reset(self, initial_position: Tuple[int, int]):
        """重置AGV状态"""
        self.position = initial_position
        self.current_load = 0
        self.status = AGVStatus.IDLE
        self.carrying_tasks.clear()
        self.target_task_id = None
        self.target_position = None
        
        # 重置统计信息
        self.total_distance = 0
        self.idle_time = 0
        self.load_time = 0
        self.total_tasks_completed = 0
        self.position_history = [initial_position]
    
    def move_to(self, new_position: Tuple[int, int]):
        """移动到新位置"""
        if new_position != self.position:
            # 计算移动距离
            distance = abs(new_position[0] - self.position[0]) + abs(new_position[1] - self.position[1])
            self.total_distance += distance
            
            # 更新位置
            self.position = new_position
            self.position_history.append(new_position)
            self.status = AGVStatus.MOVING
    
    def add_task(self, task_id: int, task_weight: int) -> bool:
        """添加任务到载重
        
        Args:
            task_id: 任务ID
            task_weight: 任务重量
            
        Returns:
            是否添加成功
        """
        if self.current_load + task_weight <= self.max_capacity:
            self.carrying_tasks.append(task_id)
            self.current_load += task_weight
            self.status = AGVStatus.LOADING
            return True
        return False
    
    def remove_all_tasks(self) -> List[int]:
        """移除所有任务（卸货）
        
        Returns:
            移除的任务ID列表
        """
        removed_tasks = self.carrying_tasks.copy()
        self.carrying_tasks.clear()
        self.current_load = 0
        self.status = AGVStatus.UNLOADING
        self.total_tasks_completed += len(removed_tasks)
        return removed_tasks
    
    def set_target(self, task_id: Optional[int], position: Optional[Tuple[int, int]]):
        """设置目标任务和位置"""
        self.target_task_id = task_id
        self.target_position = position
    
    def get_load_utilization(self) -> float:
        """获取载重利用率"""
        return self.current_load / self.max_capacity
    
    def get_remaining_capacity(self) -> int:
        """获取剩余载重容量"""
        return self.max_capacity - self.current_load
    
    def is_at_position(self, position: Tuple[int, int]) -> bool:
        """检查是否在指定位置"""
        return self.position == position
    
    def get_state_vector(self, map_width: int, map_height: int) -> np.ndarray:
        """获取AGV状态向量（5维）
        
        Returns:
            [x_norm, y_norm, load_norm, target_norm, queue_norm]
        """
        # 位置归一化
        x_norm = self.position[0] / (map_width - 1)
        y_norm = self.position[1] / (map_height - 1)
        
        # 载重归一化
        load_norm = self.current_load / self.max_capacity
        
        # 目标任务归一化（-1表示无目标任务）
        target_norm = (self.target_task_id / ENV_CONFIG.NUM_TASKS) if self.target_task_id is not None else -1.0
        
        # 任务队列长度归一化
        max_queue = self.max_capacity // 5  # 假设最小任务重量为5
        queue_norm = len(self.carrying_tasks) / max_queue
        
        return np.array([x_norm, y_norm, load_norm, target_norm, queue_norm], dtype=np.float32)
    
    def update_statistics(self):
        """更新统计信息"""
        if self.current_load > 0:
            self.load_time += 1
        else:
            self.idle_time += 1


class AGVManager:
    """AGV管理器类"""
    
    def __init__(self, map_manager: MapManager):
        """初始化AGV管理器
        
        Args:
            map_manager: 地图管理器实例
        """
        self.map_manager = map_manager
        self.num_agvs = ENV_CONFIG.NUM_AGVS
        
        # AGV列表
        self.agvs: List[AGV] = []
        
        # 初始化AGV
        self._initialize_agvs()
    
    def _initialize_agvs(self):
        """初始化所有AGV"""
        # 获取初始位置（地图下方区域）
        initial_positions = self._get_initial_positions()
        
        for i in range(self.num_agvs):
            agv = AGV(agv_id=i, initial_position=initial_positions[i])
            self.agvs.append(agv)
        
        print(f"初始化{self.num_agvs}个AGV:")
        for agv in self.agvs:
            print(f"  AGV{agv.id}: 初始位置{agv.position}")
    
    def _get_initial_positions(self) -> List[Tuple[int, int]]:
        """获取AGV初始位置（地图下方停车区域）"""
        positions = []
        
        # 在地图下方找到可通行位置作为停车区域
        bottom_y = self.map_manager.height - 1
        
        # 寻找底部可通行位置
        bottom_positions = []
        for x in range(self.map_manager.width):
            if self.map_manager.is_walkable(x, bottom_y):
                bottom_positions.append((x, bottom_y))
        
        # 如果底部位置不够，向上扩展
        if len(bottom_positions) < self.num_agvs:
            for y in range(self.map_manager.height - 2, -1, -1):
                for x in range(self.map_manager.width):
                    if self.map_manager.is_walkable(x, y):
                        bottom_positions.append((x, y))
                        if len(bottom_positions) >= self.num_agvs:
                            break
                if len(bottom_positions) >= self.num_agvs:
                    break
        
        # 选择前num_agvs个位置
        if len(bottom_positions) >= self.num_agvs:
            # 均匀分布选择
            indices = np.linspace(0, len(bottom_positions) - 1, self.num_agvs, dtype=int)
            positions = [bottom_positions[i] for i in indices]
        else:
            raise ValueError(f"可用初始位置不足: 需要{self.num_agvs}, 找到{len(bottom_positions)}")
        
        return positions
    
    def reset(self):
        """重置所有AGV状态"""
        initial_positions = self._get_initial_positions()
        for i, agv in enumerate(self.agvs):
            agv.reset(initial_positions[i])
    
    def get_agv(self, agv_id: int) -> Optional[AGV]:
        """根据ID获取AGV"""
        if 0 <= agv_id < len(self.agvs):
            return self.agvs[agv_id]
        return None
    
    def get_all_positions(self) -> List[Tuple[int, int]]:
        """获取所有AGV位置"""
        return [agv.position for agv in self.agvs]
    
    def check_collision(self, agv_id: int, new_position: Tuple[int, int]) -> bool:
        """检查移动是否会导致碰撞
        
        Args:
            agv_id: AGV ID
            new_position: 新位置
            
        Returns:
            是否会碰撞
        """
        for other_agv in self.agvs:
            if other_agv.id != agv_id and other_agv.position == new_position:
                return True
        return False
    
    def move_agv(self, agv_id: int, action: int) -> bool:
        """移动AGV
        
        Args:
            agv_id: AGV ID
            action: 移动动作
            
        Returns:
            是否移动成功
        """
        agv = self.get_agv(agv_id)
        if not agv:
            return False
        
        # 获取移动向量
        if action not in ACTION_CONFIG.MOTION_VECTORS:
            return False
        
        dx, dy = ACTION_CONFIG.MOTION_VECTORS[action]
        new_position = (agv.position[0] + dx, agv.position[1] + dy)
        
        # 检查新位置是否可通行
        if not self.map_manager.is_walkable(new_position[0], new_position[1]):
            return False
        
        # 检查是否会碰撞
        if self.check_collision(agv_id, new_position):
            return False
        
        # 执行移动
        agv.move_to(new_position)
        return True
    
    def assign_task_to_agv(self, agv_id: int, task: Task) -> bool:
        """分配任务给AGV
        
        Args:
            agv_id: AGV ID
            task: 任务对象
            
        Returns:
            是否分配成功
        """
        agv = self.get_agv(agv_id)
        if not agv:
            return False
        
        return agv.add_task(task.id, task.weight)
    
    def unload_agv_tasks(self, agv_id: int) -> List[int]:
        """卸载AGV的所有任务
        
        Args:
            agv_id: AGV ID
            
        Returns:
            卸载的任务ID列表
        """
        agv = self.get_agv(agv_id)
        if not agv:
            return []
        
        return agv.remove_all_tasks()
    
    def get_agvs_at_position(self, position: Tuple[int, int]) -> List[AGV]:
        """获取指定位置的AGV"""
        return [agv for agv in self.agvs if agv.position == position]
    
    def get_nearby_agvs(self, agv_id: int, max_distance: int = 5) -> List[AGV]:
        """获取附近的AGV
        
        Args:
            agv_id: 中心AGV ID
            max_distance: 最大距离
            
        Returns:
            附近的AGV列表
        """
        center_agv = self.get_agv(agv_id)
        if not center_agv:
            return []
        
        nearby_agvs = []
        for agv in self.agvs:
            if agv.id != agv_id:
                distance = self.map_manager.manhattan_distance(center_agv.position, agv.position)
                if distance <= max_distance:
                    nearby_agvs.append(agv)
        
        return nearby_agvs
    
    def get_all_agv_states(self) -> np.ndarray:
        """获取所有AGV的状态向量
        
        Returns:
            形状为(num_agvs, 5)的状态矩阵
        """
        states = []
        for agv in self.agvs:
            state = agv.get_state_vector(
                self.map_manager.width,
                self.map_manager.height
            )
            states.append(state)
        
        return np.array(states, dtype=np.float32)
    
    def update_all_statistics(self):
        """更新所有AGV的统计信息"""
        for agv in self.agvs:
            agv.update_statistics()
    
    def get_system_statistics(self) -> Dict:
        """获取系统统计信息"""
        total_load_time = sum(agv.load_time for agv in self.agvs)
        total_time = sum(agv.load_time + agv.idle_time for agv in self.agvs)
        
        return {
            'total_distance': sum(agv.total_distance for agv in self.agvs),
            'average_load_utilization': np.mean([agv.get_load_utilization() for agv in self.agvs]),
            'system_load_utilization': total_load_time / total_time if total_time > 0 else 0.0,
            'total_tasks_completed': sum(agv.total_tasks_completed for agv in self.agvs),
            'average_idle_time': np.mean([agv.idle_time for agv in self.agvs]),
            'collision_count': 0  # 需要在环境中统计
        }
    
    def print_status(self):
        """打印AGV状态（用于调试）"""
        print("=== AGV状态 ===")
        for agv in self.agvs:
            print(f"AGV{agv.id}: 位置{agv.position}, 载重{agv.current_load}/{agv.max_capacity}, "
                  f"状态{agv.status.name}, 携带任务{agv.carrying_tasks}")
        
        stats = self.get_system_statistics()
        print(f"系统统计: 总距离{stats['total_distance']}, "
              f"平均载重利用率{stats['average_load_utilization']:.2%}, "
              f"完成任务数{stats['total_tasks_completed']}")
        print()


if __name__ == "__main__":
    # 测试AGV管理器
    from envs.map_manager import MapManager
    
    map_manager = MapManager()
    agv_manager = AGVManager(map_manager)
    
    agv_manager.print_status()
    
    # 测试移动
    print("测试AGV移动:")
    success = agv_manager.move_agv(0, ACTION_CONFIG.MOVE_UP)
    print(f"AGV 0向上移动: {success}")
    
    agv_manager.print_status()
