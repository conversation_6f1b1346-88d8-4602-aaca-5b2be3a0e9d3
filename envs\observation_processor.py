"""
观测处理器 - 标准化77维观测空间
基于simplified_research_methodology.md实现标准化的观测空间处理
"""

import numpy as np
import torch
from typing import Dict, List, Tuple, Any, Optional
from dataclasses import dataclass

from utils.config import ENV_CONFIG, STATE_CONFIG, ACTION_CONFIG


@dataclass
class ObservationSpec:
    """观测空间规格定义"""
    
    # AGV状态维度（5维）
    AGV_STATE_DIM: int = 5
    # 任务状态维度（4维 × 16任务 = 64维）
    TASK_STATE_DIM: int = 64
    # 全局状态维度（8维）
    GLOBAL_STATE_DIM: int = 8
    # 总观测维度
    TOTAL_OBS_DIM: int = 77
    
    def __post_init__(self):
        """验证观测维度"""
        expected_total = self.AGV_STATE_DIM + self.TASK_STATE_DIM + self.GLOBAL_STATE_DIM
        if expected_total != self.TOTAL_OBS_DIM:
            raise ValueError(f"观测维度不匹配: {expected_total} != {self.TOTAL_OBS_DIM}")


class StandardizedObservationProcessor:
    """标准化观测处理器
    
    确保观测空间完全符合simplified_research_methodology.md规格：
    - AGV状态：5维 [x_norm, y_norm, load_ratio, target_unload, task_count]
    - 任务状态：64维 (16任务 × 4维) [x_norm, y_norm, weight_norm, status]
    - 全局状态：8维 [completion_rate, active_tasks, avg_load, collision_rate, time_progress, congestion, efficiency, path_optimality]
    """
    
    def __init__(self):
        self.spec = ObservationSpec()
        
        # 归一化参数
        self.position_scale = np.array([ENV_CONFIG.MAP_WIDTH - 1, ENV_CONFIG.MAP_HEIGHT - 1])
        self.weight_mapping = {5: 0.5, 10: 1.0}  # 重量归一化映射
        
    def process_agv_state(self, agv, map_width: int, map_height: int) -> np.ndarray:
        """处理单个AGV状态为5维向量
        
        Args:
            agv: AGV对象
            map_width: 地图宽度
            map_height: 地图高度
            
        Returns:
            agv_state: [x_norm, y_norm, load_ratio, target_unload, task_count]
        """
        if agv is None:
            return np.zeros(self.spec.AGV_STATE_DIM, dtype=np.float32)
        
        # 1. 位置归一化 [0, 1]
        x_norm = agv.position[0] / (map_width - 1)
        y_norm = agv.position[1] / (map_height - 1)
        
        # 2. 载重比例 [0, 1]
        load_ratio = agv.current_load / agv.max_capacity
        
        # 3. 目标卸货点 [0, 1] (0=左上角, 1=右上角)
        target_unload = 0.0  # 默认左上角
        if hasattr(agv, 'target_unload_zone'):
            target_unload = float(agv.target_unload_zone)
        
        # 4. 携带任务数量归一化 [0, 1]
        max_tasks = agv.max_capacity // ENV_CONFIG.MIN_TASK_WEIGHT  # 最大可携带任务数
        task_count = len(agv.carrying_tasks) / max_tasks if max_tasks > 0 else 0.0
        
        return np.array([x_norm, y_norm, load_ratio, target_unload, task_count], dtype=np.float32)
    
    def process_task_states(self, tasks: List, map_width: int, map_height: int) -> np.ndarray:
        """处理所有任务状态为64维向量
        
        Args:
            tasks: 任务列表
            map_width: 地图宽度
            map_height: 地图高度
            
        Returns:
            task_states: [16任务 × 4维] = 64维
        """
        task_states = np.zeros((ENV_CONFIG.NUM_TASKS, 4), dtype=np.float32)
        
        for i in range(ENV_CONFIG.NUM_TASKS):
            if i < len(tasks) and tasks[i] is not None:
                task = tasks[i]
                
                # 1. 位置归一化 [0, 1]
                x_norm = task.position[0] / (map_width - 1)
                y_norm = task.position[1] / (map_height - 1)
                
                # 2. 重量归一化 [0.5, 1.0]
                weight_norm = self.weight_mapping.get(task.weight, 0.5)
                
                # 3. 状态值 [0, 1, 2]
                status_value = float(task.status.value)
                
                task_states[i] = [x_norm, y_norm, weight_norm, status_value]
        
        return task_states.flatten()  # 展平为64维
    
    def process_global_state(self, env_state: Dict[str, Any]) -> np.ndarray:
        """处理全局状态为8维向量
        
        Args:
            env_state: 环境状态字典
            
        Returns:
            global_state: [completion_rate, active_tasks, avg_load, collision_rate, 
                          time_progress, congestion, efficiency, path_optimality]
        """
        # 1. 任务完成率 [0, 1]
        completion_rate = env_state.get('completion_rate', 0.0)
        
        # 2. 活跃任务比例 [0, 1]
        active_tasks = env_state.get('active_task_count', 0) / ENV_CONFIG.NUM_TASKS
        
        # 3. 平均载重利用率 [0, 1]
        system_stats = env_state.get('system_stats', {})
        avg_load = system_stats.get('average_load_utilization', 0.0)
        
        # 4. 碰撞率 [0, 1]
        max_collisions = ENV_CONFIG.NUM_AGVS * env_state.get('max_episode_steps', 1000) * 0.1
        collision_rate = min(env_state.get('collision_count', 0) / max_collisions, 1.0)
        
        # 5. 时间进度 [0, 1]
        time_progress = env_state.get('current_step', 0) / env_state.get('max_episode_steps', 1000)
        
        # 6. 系统拥堵程度 [0, 1] (简化计算)
        congestion = min(collision_rate * 2.0, 1.0)  # 基于碰撞率估算
        
        # 7. 整体系统效率 [0, 1]
        efficiency = completion_rate * avg_load if avg_load > 0 else 0.0
        
        # 8. 路径最优性评分 [0, 1] (简化)
        path_optimality = max(0.0, 1.0 - collision_rate - congestion * 0.5)
        
        return np.array([
            completion_rate, active_tasks, avg_load, collision_rate,
            time_progress, congestion, efficiency, path_optimality
        ], dtype=np.float32)
    
    def create_unified_observation(self, agv_states: np.ndarray, task_states: np.ndarray, 
                                 global_state: np.ndarray) -> Dict[str, torch.Tensor]:
        """创建统一的观测字典，用于注意力模型
        
        Args:
            agv_states: [num_agvs, 5] AGV状态
            task_states: [64] -> [num_tasks, 4] 任务状态
            global_state: [8] 全局状态
            
        Returns:
            unified_obs: 统一观测字典
        """
        # 重塑任务状态
        task_states_reshaped = task_states.reshape(ENV_CONFIG.NUM_TASKS, 4)
        
        # 转换为张量
        agv_tensor = torch.from_numpy(agv_states).float()
        task_tensor = torch.from_numpy(task_states_reshaped).float()
        global_tensor = torch.from_numpy(global_state).float()
        
        # 添加批次维度
        if len(agv_tensor.shape) == 2:
            agv_tensor = agv_tensor.unsqueeze(0)
        if len(task_tensor.shape) == 2:
            task_tensor = task_tensor.unsqueeze(0)
        if len(global_tensor.shape) == 1:
            global_tensor = global_tensor.unsqueeze(0)
        
        return {
            'agv_states': agv_tensor,
            'task_states': task_tensor,
            'global_state': global_tensor
        }
    
    def process_multi_agent_observations(self, raw_observations: Dict[str, np.ndarray],
                                       env_state: Dict[str, Any]) -> Dict[str, Dict[str, torch.Tensor]]:
        """处理多智能体观测
        
        Args:
            raw_observations: 原始观测字典 {agent_id: obs_vector}
            env_state: 环境状态字典
            
        Returns:
            processed_obs: 处理后的观测字典 {agent_id: unified_obs}
        """
        processed_observations = {}
        
        # 提取全局状态（所有智能体共享）
        global_state = self.process_global_state(env_state)
        
        for agent_id, obs_vector in raw_observations.items():
            if len(obs_vector) != self.spec.TOTAL_OBS_DIM:
                raise ValueError(f"观测维度错误: {len(obs_vector)} != {self.spec.TOTAL_OBS_DIM}")
            
            # 分解观测向量
            agv_state = obs_vector[:self.spec.AGV_STATE_DIM]
            task_states = obs_vector[self.spec.AGV_STATE_DIM:self.spec.AGV_STATE_DIM + self.spec.TASK_STATE_DIM]
            # global_state已经单独处理
            
            # 创建统一观测
            agv_states_batch = agv_state.reshape(1, -1)  # [1, 5]
            unified_obs = self.create_unified_observation(agv_states_batch, task_states, global_state)
            
            processed_observations[agent_id] = unified_obs
        
        return processed_observations
    
    def validate_observation_space(self, observation: np.ndarray) -> bool:
        """验证观测空间是否符合规格
        
        Args:
            observation: 观测向量
            
        Returns:
            is_valid: 是否有效
        """
        if len(observation) != self.spec.TOTAL_OBS_DIM:
            return False
        
        # 检查数值范围
        agv_state = observation[:self.spec.AGV_STATE_DIM]
        task_states = observation[self.spec.AGV_STATE_DIM:self.spec.AGV_STATE_DIM + self.spec.TASK_STATE_DIM]
        global_state = observation[self.spec.AGV_STATE_DIM + self.spec.TASK_STATE_DIM:]
        
        # AGV状态应该在合理范围内
        if not (np.all(agv_state >= -1.0) and np.all(agv_state <= 1.0)):
            return False
        
        # 任务状态检查
        task_states_reshaped = task_states.reshape(ENV_CONFIG.NUM_TASKS, 4)
        for task_state in task_states_reshaped:
            # 位置应该在[0,1]范围内
            if not (0.0 <= task_state[0] <= 1.0 and 0.0 <= task_state[1] <= 1.0):
                return False
            # 重量应该是0.5或1.0
            if task_state[2] not in [0.0, 0.5, 1.0]:
                return False
            # 状态应该是0,1,2
            if task_state[3] not in [0.0, 1.0, 2.0]:
                return False
        
        # 全局状态应该在[0,1]范围内
        if not (np.all(global_state >= 0.0) and np.all(global_state <= 1.0)):
            return False
        
        return True
    
    def get_observation_info(self) -> Dict[str, Any]:
        """获取观测空间信息"""
        return {
            'total_dim': self.spec.TOTAL_OBS_DIM,
            'agv_state_dim': self.spec.AGV_STATE_DIM,
            'task_state_dim': self.spec.TASK_STATE_DIM,
            'global_state_dim': self.spec.GLOBAL_STATE_DIM,
            'agv_state_components': ['x_norm', 'y_norm', 'load_ratio', 'target_unload', 'task_count'],
            'task_state_components': ['x_norm', 'y_norm', 'weight_norm', 'status'] * ENV_CONFIG.NUM_TASKS,
            'global_state_components': [
                'completion_rate', 'active_tasks', 'avg_load', 'collision_rate',
                'time_progress', 'congestion', 'efficiency', 'path_optimality'
            ]
        }


class StandardizedActionProcessor:
    """标准化动作空间处理器

    确保动作空间完全符合simplified_research_methodology.md规格：
    - 高层动作：18维 (0:保持当前, 1-16:选择任务, 17:前往卸货)
    - 低层动作：5维 (0:上, 1:下, 2:左, 3:右, 4:等待)
    """

    def __init__(self):
        self.task_action_dim = ACTION_CONFIG.TASK_ACTION_DIM  # 18
        self.motion_action_dim = ACTION_CONFIG.MOTION_ACTION_DIM  # 5

        # 动作映射
        self.task_action_mapping = {
            0: "保持当前",
            **{i: f"选择任务{i}" for i in range(1, 17)},
            17: "前往卸货"
        }

        self.motion_action_mapping = {
            0: "向上移动",
            1: "向下移动",
            2: "向左移动",
            3: "向右移动",
            4: "等待"
        }

        self.motion_vectors = ACTION_CONFIG.MOTION_VECTORS

    def validate_actions(self, task_action: int, motion_action: int) -> bool:
        """验证动作是否有效

        Args:
            task_action: 任务选择动作 [0-17]
            motion_action: 运动控制动作 [0-4]

        Returns:
            is_valid: 是否有效
        """
        task_valid = 0 <= task_action < self.task_action_dim
        motion_valid = 0 <= motion_action < self.motion_action_dim
        return task_valid and motion_valid

    def decode_actions(self, task_action: int, motion_action: int) -> Dict[str, Any]:
        """解码动作为可读信息

        Args:
            task_action: 任务选择动作
            motion_action: 运动控制动作

        Returns:
            action_info: 动作信息字典
        """
        return {
            'task_action': {
                'id': task_action,
                'description': self.task_action_mapping.get(task_action, "未知动作")
            },
            'motion_action': {
                'id': motion_action,
                'description': self.motion_action_mapping.get(motion_action, "未知动作"),
                'vector': self.motion_vectors.get(motion_action, (0, 0))
            }
        }

    def process_model_output(self, task_logits: torch.Tensor,
                           motion_logits: torch.Tensor) -> Dict[str, torch.Tensor]:
        """处理模型输出为标准化动作

        Args:
            task_logits: [batch, num_agvs, 18] 任务选择logits
            motion_logits: [batch, num_agvs, 5] 运动控制logits

        Returns:
            actions: 标准化动作字典
        """
        # 应用softmax获取概率分布
        task_probs = torch.softmax(task_logits, dim=-1)
        motion_probs = torch.softmax(motion_logits, dim=-1)

        # 采样动作
        task_dist = torch.distributions.Categorical(task_probs)
        motion_dist = torch.distributions.Categorical(motion_probs)

        task_actions = task_dist.sample()
        motion_actions = motion_dist.sample()

        return {
            'task_actions': task_actions,
            'motion_actions': motion_actions,
            'task_probs': task_probs,
            'motion_probs': motion_probs,
            'task_log_probs': task_dist.log_prob(task_actions),
            'motion_log_probs': motion_dist.log_prob(motion_actions)
        }

    def get_action_space_info(self) -> Dict[str, Any]:
        """获取动作空间信息"""
        return {
            'task_action_dim': self.task_action_dim,
            'motion_action_dim': self.motion_action_dim,
            'total_action_combinations': self.task_action_dim * self.motion_action_dim,
            'task_action_mapping': self.task_action_mapping,
            'motion_action_mapping': self.motion_action_mapping,
            'motion_vectors': self.motion_vectors
        }
